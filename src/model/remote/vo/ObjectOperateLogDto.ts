import ObjectFieldOperateLogDto from 'model/remote/vo/ObjectFieldOperateLogDto'

export default class ObjectOperateLogDto {
  // 
  appId: Nullable<string> = null
  // 
  appName: Nullable<string> = null
  // 
  moduleId: Nullable<string> = null
  // 
  moduleName: Nullable<string> = null
  // 
  action: Nullable<string> = null
  // 
  operateTime: Nullable<Date> = null
  // 
  operatorId: Nullable<string> = null
  // 
  operatorName: Nullable<string> = null
  // 
  ip: Nullable<string> = null
  // 
  objectType: Nullable<string> = null
  // 
  objectPid: Nullable<string> = null
  // 
  objectBids: string[] = []
  // 
  changeFields: ObjectFieldOperateLogDto[] = []
  // 
  operateResult: Nullable<string> = null
  // 
  remark: Nullable<string> = null
}