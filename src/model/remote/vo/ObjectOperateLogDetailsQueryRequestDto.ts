import PageQueryRequestDto from 'model/remote/vo/PageQueryRequestDto'

export default class ObjectOperateLogDetailsQueryRequestDto extends PageQueryRequestDto {
  // 
  appIdEquals: Nullable<string> = null
  // 
  objectTypeEquals: Nullable<string> = null
  // 
  objectPidEquals: Nullable<string> = null
  // 
  operatorIdEquals: Nullable<string> = null
  // 
  operateTimeGreaterOrEquals: Nullable<Date> = null
  // 
  operateTimeLessOrEquals: Nullable<Date> = null
  // 
  operateResultEquals: Nullable<string> = null
  // 
  actionEquals: Nullable<string> = null
  // 
  moduleIdEquals: Nullable<string> = null
  // 
  moduleIdIn: string[] = []
}