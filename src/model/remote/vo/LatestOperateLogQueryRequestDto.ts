import PageQueryRequestDto from 'model/remote/vo/PageQueryRequestDto'

export default class LatestOperateLogQueryRequestDto extends PageQueryRequestDto {
  // 
  appIdEquals: Nullable<string> = null
  // 
  moduleIdEquals: Nullable<string> = null
  // 
  objectTypeEquals: Nullable<string> = null
  // 
  objectBidEquals: Nullable<string> = null
  // 
  operatorIdEquals: Nullable<string> = null
  // 
  operateTimeGreaterOrEquals: Nullable<Date> = null
  // 
  operateTimeLessOrEquals: Nullable<Date> = null
}