export default class ExtendedInfo {
  // 
  policyId: Nullable<string> = null
  // 
  policyNameCn: Nullable<string> = null
  // 
  policyNameEn: Nullable<string> = null
  // 
  price: Nullable<number> = null
  // 
  currency: Nullable<string> = null
  // 已废弃
  currencyPrice: Nullable<number> = null
  // 
  type: Nullable<string> = null
  // 
  beginDate: Nullable<Date> = null
  // 
  endDate: Nullable<Date> = null
  // 
  endHourReading: Nullable<number> = null
  // 
  fromShippingDate: Nullable<number> = null
  // 
  fromDeliveryDate: Nullable<number> = null
  // 
  dateUnit: Nullable<string> = null
}