import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import ProdGroupCommissionRateListOut from 'model/remote/price/model/vo/subsidiarycommissionrate/ProdGroupCommissionRateListOut'
import { OfficeType } from 'model/remote/data/model/po/office/OfficeType'

export default class SubsidiaryCommissionRateListOut extends LtcEntityVo {
  // 唯一标识
  id: Nullable<string> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // 英文子公司名称
  subsidiaryNameEn: Nullable<string> = null
  // 办事处名称
  officeName: Nullable<string> = null
  // 英文办事处名称
  officeNameEn: Nullable<string> = null
  // 办事处类型
  officeType: Nullable<OfficeType> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 销售模式
  saleMode: Nullable<string> = null
  // 出口类型
  exportType: Nullable<string> = null
  // 有效期开始时间
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndDate: Nullable<Date> = null
  // 产品线佣金率
  commissionRatesJson: Nullable<string> = null
  // 产品线佣金率
  commissionRates: ProdGroupCommissionRateListOut[] = []
  // 备注
  note: Nullable<string> = null
}