import ProdGroupCommissionRateSaveIn from 'model/remote/price/model/vo/subsidiarycommissionrate/ProdGroupCommissionRateSaveIn'
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType'

export default class SubsidiaryCommissionRateSaveIn {
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 办事处ID
  officeId: Nullable<number> = null
  // 国家ID
  countryId: Nullable<number> = null
  // 出口类型
  exportType: Nullable<ExportType> = null
  // 有效期开始时间
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndDate: Nullable<Date> = null
  // 产品线佣金率
  commissionRates: ProdGroupCommissionRateSaveIn[] = []
  // 备注
  note: Nullable<string> = null
}