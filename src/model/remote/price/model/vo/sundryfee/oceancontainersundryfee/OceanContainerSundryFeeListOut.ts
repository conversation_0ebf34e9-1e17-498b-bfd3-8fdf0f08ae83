import ContainerBasicPriceVo from 'model/remote/price/model/vo/ContainerBasicPriceVo'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class OceanContainerSundryFeeListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 柜型运费单价json
  basicPricesJson: Nullable<string> = null
  // 柜型杂费单价
  basicPrices: ContainerBasicPriceVo[] = []
  // 加价系数
  raiseRatio: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}