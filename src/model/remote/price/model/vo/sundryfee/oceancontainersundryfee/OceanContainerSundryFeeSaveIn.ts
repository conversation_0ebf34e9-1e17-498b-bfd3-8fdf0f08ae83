import ContainerBasicPriceVo from 'model/remote/price/model/vo/ContainerBasicPriceVo'

export default class OceanContainerSundryFeeSaveIn {
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 柜型杂费单价
  basicPrices: ContainerBasicPriceVo[] = []
  // 加价率
  raiseRatio: Nullable<number> = null
}