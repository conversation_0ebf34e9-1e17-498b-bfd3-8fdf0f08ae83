export default class OceanContainerSundryFeeListIn {
  // 起运港/起运站代码等于
  domesticFreightPortCodeEquals: Nullable<string> = null
  // 起运港/起运站代码范围in
  domesticFreightPortCodeIn: string[] = []
  // 有效期开始日期起始于
  effectiveStartDateStart: Nullable<Date> = null
  // 有效期开始日期截至于
  effectiveStartDateEnd: Nullable<Date> = null
  // 有效期结束日期起始于
  effectiveEndDateStart: Nullable<Date> = null
  // 有效期结束日期截至于
  effectiveEndDateEnd: Nullable<Date> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // id在...范围内
  idIn: string[] = []
}