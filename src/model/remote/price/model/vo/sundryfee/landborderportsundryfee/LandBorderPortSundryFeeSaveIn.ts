export default class LandBorderPortSundryFeeSaveIn {
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 机型id
  prodModuleIds: string[] = []
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 杂费单价
  basicPrice: Nullable<number> = null
  // 加价率
  raiseRatio: Nullable<number> = null
}