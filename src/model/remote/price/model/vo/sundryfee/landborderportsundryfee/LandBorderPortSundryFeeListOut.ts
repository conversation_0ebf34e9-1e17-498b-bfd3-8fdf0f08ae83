import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class LandBorderPortSundryFeeListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // en国际产品线分类
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 机型代码
  prodModuleCodes: string[] = []
  // 杂费单价
  basicPrice: Nullable<number> = null
  // 报价
  price: Nullable<number> = null
  // 加价系数
  raiseRatio: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}