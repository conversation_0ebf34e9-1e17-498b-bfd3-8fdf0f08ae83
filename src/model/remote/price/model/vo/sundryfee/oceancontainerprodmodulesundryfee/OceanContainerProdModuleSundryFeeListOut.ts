import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class OceanContainerProdModuleSundryFeeListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 机型ID
  prodModuleId: Nullable<string> = null
  // 机型代码
  prodModuleCode: Nullable<string> = null
  // 机型名称
  prodModuleName: Nullable<string> = null
  // 机型名称(英文)
  prodModuleNameEn: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 杂费单价
  price: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 启用状态，0-禁用，1-启用
  enabled: Nullable<EnabledState> = null
  // 版本号
  version: Nullable<number> = null
  // 业务主键
  businessKey: Nullable<string> = null
}