import { PackingFeeDetailType } from 'model/remote/price/model/po/sundryfee/packingfee/PackingFeeDetailType'

export default class PackingFeeDetailVo {
  // 柜型
  cabinetType: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 基础单价
  basicPrice: Nullable<number> = null
  // 报价
  price: Nullable<number> = null
  // 运输模式
  type: Nullable<PackingFeeDetailType> = null
}