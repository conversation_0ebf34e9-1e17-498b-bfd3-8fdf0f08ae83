import PackingFeeDetailVo from 'model/remote/price/model/vo/sundryfee/packingfee/PackingFeeDetailVo'

export default class PackingFeeSaveIn {
  // id
  id: Nullable<string> = null
  // 机型id
  prodModuleIds: string[] = []
  // 加价系数
  raiseRatio: Nullable<number> = null
  // 装箱费明细
  prices: PackingFeeDetailVo[] = []
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 版本号
  version: Nullable<number> = null
}