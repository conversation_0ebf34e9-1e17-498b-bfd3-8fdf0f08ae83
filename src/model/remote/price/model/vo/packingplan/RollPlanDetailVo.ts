import RollPlanSpecificDetailVo from 'model/remote/price/model/vo/packingplan/RollPlanSpecificDetailVo'

export default class RollPlanDetailVo {
  // 装运方案id
  packingPlanId: Nullable<string> = null
  // 装运方案明细ID
  id: Nullable<string> = null
  // 发运台量
  transportQuantity: Nullable<number> = null
  // 方案说明
  statement: Nullable<string> = null
  // 是否拆解
  decompose: Nullable<boolean> = null
  // 是否最优
  optimal: Nullable<boolean> = null
  // 总重量
  totalWeight: Nullable<number> = null
  // 具体明细
  specificDetails: RollPlanSpecificDetailVo[] = []
  // 组内序号
  sort: Nullable<number> = null
}