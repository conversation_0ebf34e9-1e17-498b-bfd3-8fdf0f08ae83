import PackingPlanContainerDetailVo from 'model/remote/price/model/vo/packingplan/PackingPlanContainerDetailVo'
import PackingPlanRollDetailVo from 'model/remote/price/model/vo/packingplan/PackingPlanRollDetailVo'
import { PackingPlanType } from 'model/remote/price/model/po/packingplan/PackingPlanType'

export default class PackingPlanSaveIn {
  // id
  id: Nullable<string> = null
  // 机型id
  prodModelId: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 配置描述（营销），当装运方式为集装箱时为空
  configDesc: Nullable<string> = null
  // 装运方式
  type: Nullable<PackingPlanType> = null
  // 集装箱装运方案明细
  containerDetails: PackingPlanContainerDetailVo[] = []
  // 滚装/散杂装运方案明细
  rollDetails: PackingPlanRollDetailVo[] = []
  // 版本号
  version: Nullable<number> = null
}