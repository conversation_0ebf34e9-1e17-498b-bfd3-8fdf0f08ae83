import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class PackingPlanListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 机型Id
  prodModelId: Nullable<string> = null
  // 机型Code
  prodModelCode: Nullable<string> = null
  // 配置描述（营销），当装运方式为集装箱时为空。为空时，前端展示为-
  configDesc: Nullable<string> = null
  // 国际产品名称
  i18ProdGroupSortName: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 装运方式
  type: Nullable<string> = null
  // 装运方式名称
  typeName: Nullable<string> = null
  // 方案数量
  planQuantity: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
}