import PackageProtectionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionDetailVo'
import PackageProtectionRegionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionRegionDetailVo'
import { TransportType } from 'model/remote/price/model/po/TransportType'

export default class PackageProtectionForPlanDetailVo {
  // 
  detailId: Nullable<string> = null
  // 
  id: Nullable<string> = null
  // 
  mode: Nullable<TransportType> = null
  // 
  modeName: Nullable<string> = null
  // 
  effectiveStartDate: Nullable<Date> = null
  // 
  effectiveEndDate: Nullable<Date> = null
  // 
  recommendDetails: PackageProtectionDetailVo[] = []
  // 
  optionalDetails: PackageProtectionDetailVo[] = []
  // 
  regionDetails: PackageProtectionRegionDetailVo[] = []
}