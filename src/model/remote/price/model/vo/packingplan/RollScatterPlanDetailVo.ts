import IdStringName from 'model/remote/support/core/domain/IdStringName'

export default class RollScatterPlanDetailVo {
  // 装运方案明细id
  id: Nullable<string> = null
  // 装运方案id
  packingPlanId: Nullable<string> = null
  // 是否拆解
  decompose: Nullable<boolean> = null
  // 是否有动力
  power: Nullable<boolean> = null
  // 选项值明细集合
  optionValues: IdStringName[] = []
  // 选项值明细的所有选项值id排序后的md5值，机型下唯一
  optionValuesKey: Nullable<string> = null
  // 长度（毫米）
  length: Nullable<number> = null
  // 宽度（毫米）
  width: Nullable<number> = null
  // 高度（毫米）
  height: Nullable<number> = null
  // 发运台量
  transportQuantity: Nullable<number> = null
  // 是否标准
  standard: Nullable<boolean> = null
}