import ContainerPlanSpecificDetailVo from 'model/remote/price/model/vo/packingplan/ContainerPlanSpecificDetailVo'
import PackageProtectionForPlanDetailVo from 'model/remote/price/model/vo/packingplan/PackageProtectionForPlanDetailVo'

export default class ContainerPlanDetailVo {
  // 唯一标识
  id: Nullable<string> = null
  // 装运方案id
  planId: Nullable<string> = null
  // 发运台量
  transportQuantity: Nullable<number> = null
  // 是否拆解
  decompose: Nullable<boolean> = null
  // 方案说明
  statement: Nullable<string> = null
  // 是否最优
  optimal: Nullable<boolean> = null
  // 具体明细
  specificDetails: ContainerPlanSpecificDetailVo[] = []
  // 包装防护价格明细
  packageDetails: PackageProtectionForPlanDetailVo[] = []
  // 序号
  sort: Nullable<number> = null
}