import ContainerPlanDetailVo from 'model/remote/price/model/vo/packingplan/ContainerPlanDetailVo'
import ContainerPlanSpecialConfigDetailVo from 'model/remote/price/model/vo/packingplan/ContainerPlanSpecialConfigDetailVo'

export default class PackingPlanContainerDetailVo {
  // 装运方案id
  packingPlanId: Nullable<string> = null
  // 特殊配置key，由特殊配置id构成，按照正序排列，用@@拼接
  specialConfigKey: Nullable<string> = null
  // 特殊配置key，由特殊配置id构成，按照正序排列，用@@拼接，并md5编码
  specialConfigKeyMd5: Nullable<string> = null
  // 特殊配置明细
  specificConfigDetails: ContainerPlanSpecialConfigDetailVo[] = []
  // 装运方案明细
  details: ContainerPlanDetailVo[] = []
  // 序号
  groupSort: Nullable<number> = null
}