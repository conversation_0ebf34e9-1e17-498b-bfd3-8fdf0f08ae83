import { PackingPlanType } from 'model/remote/price/model/po/packingplan/PackingPlanType'

export default class PackingPlanListIn {
  // 机型id等于
  prodModelIdEquals: Nullable<string> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id在...之中
  i18ProdGroupSortIdIn: string[] = []
  // 装运方式等于
  typeEquals: Nullable<PackingPlanType> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截至
  createTimeEnd: Nullable<Date> = null
}