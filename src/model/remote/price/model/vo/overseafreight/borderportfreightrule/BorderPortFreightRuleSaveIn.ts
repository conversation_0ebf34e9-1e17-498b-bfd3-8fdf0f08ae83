import { FreightRuleSeason } from 'model/remote/price/model/po/FreightRuleSeason'

export default class BorderPortFreightRuleSaveIn {
  // 运费规则id
  id: Nullable<string> = null
  // 年份
  year: Nullable<number> = null
  // 季度
  season: Nullable<FreightRuleSeason> = null
  // 目的站代码
  destinationPortCode: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 机型id
  prodModuleIds: string[] = []
  // 基础价最小值
  minBasicPrice: Nullable<number> = null
  // 基础价最大值
  maxBasicPrice: Nullable<number> = null
  // 加价系数
  raiseRatio: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
  // 有效期起始时间
  effectiveStartDate: Nullable<Date> = null
}