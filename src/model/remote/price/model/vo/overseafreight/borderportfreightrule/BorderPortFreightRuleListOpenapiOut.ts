import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { FreightRuleSeason } from 'model/remote/price/model/po/FreightRuleSeason'

export default class BorderPortFreightRuleListOpenapiOut extends LtcEntityVo {
  // 运费规则id
  id: Nullable<string> = null
  // 年份
  year: Nullable<number> = null
  // 季度
  season: Nullable<FreightRuleSeason> = null
  // 目的站名称
  destinationPortName: Nullable<string> = null
  // 目的站英文名称
  destinationPortNameEn: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // 国际产品线分类_英文
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 机型代码
  prodModuleCodes: string[] = []
  // 报价最小值
  minPrice: Nullable<number> = null
  // 报价最大值
  maxPrice: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
}