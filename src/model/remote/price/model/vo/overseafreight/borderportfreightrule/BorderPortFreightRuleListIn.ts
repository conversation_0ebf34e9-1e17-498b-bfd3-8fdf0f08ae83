import { FreightRuleSeason } from 'model/remote/price/model/po/FreightRuleSeason'

export default class BorderPortFreightRuleListIn {
  // 年份等于
  yearEquals: Nullable<number> = null
  // 季度等于
  seasonEquals: Nullable<FreightRuleSeason> = null
  // 目的港/目的站代码等于
  destinationPortCodeEquals: Nullable<string> = null
  // 起运港/起运站代码等于
  domesticFreightPortCodeEquals: Nullable<string> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id在...之中
  i18ProdGroupSortIdIn: string[] = []
  // 机型id等于
  prodModuleIdEquals: Nullable<string> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // id在...之中
  idIn: string[] = []
}