import RegionCtrySortVo from 'model/remote/price/model/vo/regionmanagement/RegionCtrySortVo'

export default class RegionDetailVo {
  // 大区编码
  regionCode: Nullable<string> = null
  // 细分区域编码
  code: Nullable<string> = null
  // 细分区域名称
  name: Nullable<string> = null
  // 细分区域英文名称
  nameEn: Nullable<string> = null
  // 是否有全部国家列表的数据授权范围(仅在带数据授权范围查询的情况下生效): true 有，false 无;
  hasAllCtrysDataPermission: Nullable<boolean> = null
  // 国家分类
  ctrySorts: RegionCtrySortVo[] = []
  // 序号
  sort: Nullable<number> = null
}