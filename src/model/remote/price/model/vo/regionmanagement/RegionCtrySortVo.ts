import IdCodeName from 'model/remote/support/core/domain/IdCodeName'

export default class RegionCtrySortVo {
  // 大区编码
  regionCode: Nullable<string> = null
  // 细分区域编码
  regionDetailCode: Nullable<string> = null
  // 国家分类编码
  code: Nullable<string> = null
  // 国家分类名称
  name: Nullable<string> = null
  // 国家分类英文名称
  nameEn: Nullable<string> = null
  // 国家列表：id即国家id，code即国家code，name即国家名称
  ctrys: IdCodeName[] = []
  // 序号
  sort: Nullable<number> = null
}