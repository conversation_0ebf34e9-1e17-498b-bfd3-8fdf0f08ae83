export default class RegionManagementListIn {
  // 大区代码等于
  codeEquals: Nullable<string> = null
  // 大区编码/名称模糊匹配
  regionLike: Nullable<string> = null
  // 细分区域编码/名称模糊匹配
  regionDetailLike: Nullable<string> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // 最新修改时间起始于
  lastModifiedStart: Nullable<Date> = null
  // 最新修改时间截止于
  lastModifiedEnd: Nullable<Date> = null
  // 大区编码在...之中
  regionCodeIn: string[] = []
  // 细分区域编码在...之中
  regionDetailCodeIn: string[] = []
}