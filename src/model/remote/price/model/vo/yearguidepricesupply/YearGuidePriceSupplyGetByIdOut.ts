import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'
import { GuidePriceSign } from 'model/remote/price/model/po/GuidePriceSign'

export default class YearGuidePriceSupplyGetByIdOut extends LtcEntityVo {
  // 价格记录id
  id: Nullable<string> = null
  // 物料号
  materialCode: Nullable<string> = null
  // 机型
  prodModelCode: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // en国际产品线分类
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 配置说明（营销）
  config: Nullable<string> = null
  // 配置说明（营销）_英文
  configEn: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<string> = null
  // 办事处ID
  officeId: Nullable<string> = null
  // 国家ID
  countryId: Nullable<string> = null
  // 客户Id
  customId: Nullable<string> = null
  // 币种
  currencyCode: Nullable<string> = null
  // EXW指导价（含税、含佣）
  exwPrice: Nullable<number> = null
  // 有效期开始时间
  effectiveStartTime: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndTime: Nullable<Date> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 备注
  note: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 价格标识
  priceSign: Nullable<GuidePriceSign> = null
  // 特价是否限制数量
  limitMat: Nullable<boolean> = null
  // 特价限制数量
  limitQty: Nullable<number> = null
  // 特价已使用数量
  useQty: Nullable<number> = null
  // 特价是否允许继续产生折扣
  discount: Nullable<boolean> = null
}