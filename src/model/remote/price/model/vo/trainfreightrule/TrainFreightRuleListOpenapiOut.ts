import ContainerBasicPriceOpenapiVo from 'model/remote/price/model/vo/ContainerBasicPriceOpenapiVo'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class TrainFreightRuleListOpenapiOut extends LtcEntityVo {
  // 运费规则id
  id: Nullable<string> = null
  // 发货地/出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 目的港/目的站英文名称
  destinationPortNameEn: Nullable<string> = null
  // 柜型运费单价json
  basicPricesJson: Nullable<string> = null
  // 柜型运费单价
  basicPrices: ContainerBasicPriceOpenapiVo[] = []
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}