import ContainerBasicPriceVo from 'model/remote/price/model/vo/ContainerBasicPriceVo'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class OceanAndTrainFreightRuleListOut extends LtcEntityVo {
  // 运费规则id
  id: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 目的港/目的站英文名称
  destinationPortNameEn: Nullable<string> = null
  // 柜型运费单价json
  basicPricesJson: Nullable<string> = null
  // 柜型运费单价
  basicPrices: ContainerBasicPriceVo[] = []
  // 加价系数
  raiseRatio: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}