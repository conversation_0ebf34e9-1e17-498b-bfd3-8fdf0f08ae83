import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class OceanLooseFreightRuleListOut extends LtcEntityVo {
  // 运费规则id
  id: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 目的港/目的站名称
  destinationPortName: Nullable<string> = null
  // 目的港/目的站英文名称
  destinationPortNameEn: Nullable<string> = null
  // 有动力滚装运费基础价
  powerRollBasicPrice: Nullable<number> = null
  // 无动力滚装运费基础价
  unPowerRollBasicPrice: Nullable<number> = null
  // 有动力滚装运费报价
  powerRollPrice: Nullable<number> = null
  // 无动力滚装运费报价
  unPowerRollPrice: Nullable<number> = null
  // 散杂运费基础价
  disperseBasicPrice: Nullable<number> = null
  // 散杂运费报价
  dispersePrice: Nullable<number> = null
  // 加价系数
  raiseRatio: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}