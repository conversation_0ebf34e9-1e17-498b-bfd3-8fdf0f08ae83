import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class YearGuidePriceSelfSaveIn extends LtcEntityVo {
  // 指导价id
  id: Nullable<string> = null
  // 物料号
  materialCode: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 办事处ID
  officeId: Nullable<number> = null
  // 国家ID
  countryId: Nullable<number> = null
  // 币种
  currencyCode: Nullable<string> = null
  // 启用状态
  enabled: Nullable<number> = null
  // 客户ID
  customId: Nullable<number> = null
  // EXW指导价
  exwPrice: Nullable<number> = null
  // 有效期开始时间
  effectiveStartTime: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndTime: Nullable<Date> = null
  // 备注
  note: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 是否限制数量
  limitMat: Nullable<boolean> = null
  // 限制数量
  limitQty: Nullable<number> = null
  // 已使用数量
  useQty: Nullable<number> = null
  // 是否允许继续产生折扣
  discount: Nullable<boolean> = null
}