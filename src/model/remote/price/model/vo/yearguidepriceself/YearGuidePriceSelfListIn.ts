import DataPermissionLimit from 'model/remote/support/datapermission/DataPermissionLimit'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'
import { GuidePriceSign } from 'model/remote/price/model/po/GuidePriceSign'

export default class YearGuidePriceSelfListIn {
  // 物料号起始于
  materialCodeStart: Nullable<string> = null
  // 物料号等于
  materialCodeEquals: Nullable<string> = null
  // 物料号在...之中
  materialCodeIn: string[] = []
  // 子公司ID等于
  subsidiaryIdEquals: Nullable<number> = null
  // 办事处ID等于
  officeIdEquals: Nullable<number> = null
  // 国家ID等于
  countryIdIdEquals: Nullable<number> = null
  // 币种等于
  currencyCodeEquals: Nullable<string> = null
  // 机型code起始于
  prodModuleCodeStart: Nullable<string> = null
  // 机型id等于
  prodMdlIdEquals: Nullable<string> = null
  // 国家ID在...之中
  countryIdIn: number[] = []
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id在...之中
  i18ProdGroupSortIdIn: string[] = []
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 客户ID等于
  customIdEquals: Nullable<number> = null
  // 有效期开始时间
  effectiveStartTime: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndTime: Nullable<Date> = null
  // 有效期开始时间 小于等于
  effectiveStartTimeLe: Nullable<Date> = null
  // 有效期结束时间 大于等于
  effectiveEndTimeGe: Nullable<Date> = null
  // 价格记录id模糊查询
  idLike: Nullable<string> = null
  // 语言环境
  lang: Nullable<string> = null
  // 价格标识等于
  priceSignEquals: Nullable<GuidePriceSign> = null
  // 数据授权限制-子公司
  subsidiaryLimit: Nullable<DataPermissionLimit> = null
  // 数据授权限制-产品线
  prodGroupLimit: Nullable<DataPermissionLimit> = null
  // 特价申请源单等于
  specialApplyBillIdEquals: Nullable<string> = null
}