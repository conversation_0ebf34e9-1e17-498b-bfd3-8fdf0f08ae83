import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'
import { GuidePriceSign } from 'model/remote/price/model/po/GuidePriceSign'

export default class YearGuidePriceSelfListOut extends LtcEntityVo {
  // 价格记录id
  id: Nullable<string> = null
  // 物料号
  materialCode: Nullable<string> = null
  // 超级bom标识，1-是，非固定物料，0-否，固定物料
  superBomFlag: Nullable<number> = null
  // 机型
  prodModelCode: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // en国际产品线分类
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 配置说明（营销）
  config: Nullable<string> = null
  // 配置说明（营销）_英文
  configEn: Nullable<string> = null
  // 子公司id
  subsidiaryId: Nullable<number> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // en子公司名称
  subsidiaryNameEn: Nullable<string> = null
  // 办事处id
  officeId: Nullable<number> = null
  // 办事处名称
  officeName: Nullable<string> = null
  // en办事处名称
  officeNameEn: Nullable<string> = null
  // 国家id
  countryId: Nullable<number> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 客户id
  customId: Nullable<number> = null
  // 客户代码
  customNum: Nullable<string> = null
  // 客户名称
  customName: Nullable<string> = null
  // en客户名称
  customNameEn: Nullable<string> = null
  // 币种
  currencyCode: Nullable<string> = null
  // 币种名称
  currencyName: Nullable<string> = null
  // 价格类型
  priceType: Nullable<string> = null
  // EXW指导价
  exwPrice: Nullable<number> = null
  // 有效期开始时间
  effectiveStartTime: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndTime: Nullable<Date> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 备注
  note: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 价格标识
  priceSign: Nullable<GuidePriceSign> = null
  // 特价是否限制数量
  limitMat: Nullable<boolean> = null
  // 特价限制数量
  limitQty: Nullable<number> = null
  // 特价已使用数量
  useQty: Nullable<number> = null
  // 特价是否允许继续产生折扣
  discount: Nullable<boolean> = null
  // 特价申请源单
  specialApplyBillId: Nullable<string> = null
}