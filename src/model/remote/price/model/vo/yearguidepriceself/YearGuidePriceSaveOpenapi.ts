import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType'

// 年度指导价-自营/供货保存，对外
export default class YearGuidePriceSaveOpenapi {
  // 用户id
  userId: string = ''
  // 员工代码
  employeeCode: string = ''
  // 员工名称
  employeeName: string = ''
  // 出口类型
  exportType: Nullable<ExportType> = null
  // 物料号
  matCd: string = ''
  // 办事处ID
  ofcId: string = ''
  // 子公司ID
  sbsdyId: string = ''
  // 销往国ID
  ctryId: string = ''
  // 客户ID。可为空
  custId: Nullable<string> = null
  // 币种id，来自数据字典
  currencyId: string = ''
  // 指导价
  exwPrice: number = 0
  // 有效期开始时间
  effectiveStartTime: Nullable<Date> = null
  // 有效期结束时间
  effectiveEndTime: Nullable<Date> = null
}