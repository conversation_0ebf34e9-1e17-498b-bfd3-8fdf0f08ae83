import StringIdName from 'model/remote/support/core/domain/StringIdName'
import UCN from 'model/remote/vo/UCN'

export default class BpfmHiTaskInstVO {
  // 节点方式。one：或签，其他都按会签
  nodeMode: string = ''
  // 状态。completed：已执行，unclaimed：未执行
  state: string = ''
  // 任务名称
  taskName: string = ''
  // 候选人/执行人
  candidate: UCN = new UCN()
  // 执行操作
  outgoing: Nullable<StringIdName> = null
  // 审批意见
  comment: Nullable<string> = null
  // 任务抵达时间
  createTime: Date = new Date()
  // 任务完成时间
  endTime: Nullable<Date> = null
  // 任务执行时间，秒。null表示执行中
  durationInSeconds: Nullable<number> = null
  // 允许特价产生折扣
  discount: Nullable<boolean> = null
}