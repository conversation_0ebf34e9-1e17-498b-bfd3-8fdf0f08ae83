import I18SortProdMaterialGetOut from 'model/remote/price/model/vo/i18prodgroupsort/I18SortProdMaterialGetOut'
import OptionDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/OptionDetailsSpecialConfigVo'
import StaticConfigDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/StaticConfigDetailsSpecialConfigVo'
import StringIdName from 'model/remote/support/core/domain/StringIdName'

export default class I18SortProdMaterialWithDetailGetOut extends I18SortProdMaterialGetOut {
  // 固定配置明细（带特殊配置）
  staticConfigDetails: StaticConfigDetailsSpecialConfigVo[] = []
  // 选项明细（带特殊配置）
  optionDetails: OptionDetailsSpecialConfigVo[] = []
  // 特殊配置key，由特殊配置id构成，按照正序排列，用@@拼接
  specialConfigKey: Nullable<string> = null
  // 特殊配置明细
  specialConfigs: StringIdName[] = []
  // 属具数量
  accessoryQty: Nullable<number> = null
}