import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class I18ProdGroupSortSaveIn {
  // 分类id
  id: Nullable<string> = null
  // 分类名称。必填
  name: Nullable<string> = null
  // 分类名称_英文。必填。由前端控制英文格式
  nameEn: Nullable<string> = null
  // 上级分类id
  parentId: Nullable<string> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 分类依据
  note: Nullable<string> = null
  // 分类层级
  level: Nullable<number> = null
  // 序号
  sort: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
}