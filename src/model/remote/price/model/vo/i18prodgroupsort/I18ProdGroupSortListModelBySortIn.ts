export default class I18ProdGroupSortListModelBySortIn {
  // 是否查询全部机型
  allModel: Nullable<boolean> = null
  // 是否查询异常机型
  abnormalModel: Nullable<boolean> = null
  // 分类id等于
  sortIdEquals: Nullable<string> = null
  // 分类id在...之中
  sortIdIn: string[] = []
  // 机型代码模糊匹配
  prodModelCodeLike: Nullable<string> = null
  // 研发产品组id等于
  prodGroupIdEquals: Nullable<string> = null
  // 机型id在...之中
  prodModelIdIn: string[] = []
  // 需要排除关联机型的分类集合
  excludeModelSortIdIn: string[] = []
}