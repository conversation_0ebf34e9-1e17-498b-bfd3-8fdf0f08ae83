import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class I18ProdGroupSortListOut {
  // 分类id
  id: Nullable<string> = null
  // 分类名称
  name: Nullable<string> = null
  // 分类名称_英文
  nameEn: Nullable<string> = null
  // 上级分类id
  parentId: Nullable<string> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 分类依据
  note: Nullable<string> = null
  // 分类层级
  level: Nullable<number> = null
  // 序号
  sort: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
  // 最后修改时间
  lastModified: Nullable<Date> = null
}