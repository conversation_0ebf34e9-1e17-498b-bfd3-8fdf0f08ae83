import ProdModelSpecialConfigDetailNewVo from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigDetailNewVo'

export default class ProdModelSpecialConfigSaveNewIn {
  // 机型id
  prodModelId: Nullable<string> = null
  // 产品线ID
  productLineId: Nullable<string> = null
  // 产品线名称
  productLineName: Nullable<string> = null
  // 机型代码
  prodModelCode: Nullable<string> = null
  // 机型名称
  prodModelName: Nullable<string> = null
  // 机型英文
  prodModelNameEn: Nullable<string> = null
  // 配置内容
  configurations: ProdModelSpecialConfigDetailNewVo[] = []
}