import ProdModelSpecialConfigDetailNewVo from 'model/remote/price/model/vo/prodmodelspecialconfig/ProdModelSpecialConfigDetailNewVo'

export default class ProdModelSpecialConfigGetNewOut {
  // 机型id
  prodModelId: Nullable<string> = null
  // 机型代码
  prodModelCode: Nullable<string> = null
  // 机型中文
  prodModelName: Nullable<string> = null
  // 机型英文
  prodModelNameEn: Nullable<string> = null
  // 国际产品线id
  firstLevelI18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  firstLevelI18ProdGroupName: Nullable<string> = null
  // 国际产品线英文名称
  firstLevelI18ProdGroupNameEn: Nullable<string> = null
  // 配置内容
  configurations: ProdModelSpecialConfigDetailNewVo[] = []
}