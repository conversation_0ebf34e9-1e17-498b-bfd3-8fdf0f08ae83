import PackageProtectionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionDetailVo'
import PackageProtectionRegionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionRegionDetailVo'
import UcnVo from 'model/remote/support/core/domain/UcnVo'
import { TransportType } from 'model/remote/price/model/po/TransportType'

export default class PackageProtectionPriceSaveIn {
  // id
  id: Nullable<string> = null
  // 运输方式
  mode: Nullable<TransportType> = null
  // 机型
  prodModels: UcnVo[] = []
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 版本号
  version: Nullable<number> = null
  // 推荐防护
  recommendDetails: PackageProtectionDetailVo[] = []
  // 自选明细
  optionalDetails: PackageProtectionDetailVo[] = []
  // 区域客户设置
  regionDetails: PackageProtectionRegionDetailVo[] = []
}