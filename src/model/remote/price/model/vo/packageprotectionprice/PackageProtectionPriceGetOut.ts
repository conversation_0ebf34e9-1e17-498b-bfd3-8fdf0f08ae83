import PackageProtectionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionDetailVo'
import PackageProtectionRegionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionRegionDetailVo'
import UcnEnVo from 'model/remote/support/core/domain/UcnEnVo'
import UcnVo from 'model/remote/support/core/domain/UcnVo'
import { TransportType } from 'model/remote/price/model/po/TransportType'

export default class PackageProtectionPriceGetOut {
  // id
  id: Nullable<string> = null
  // 运输方式
  mode: Nullable<TransportType> = null
  // 运输方式名称
  modeName: Nullable<string> = null
  // 机型
  prodModels: UcnVo[] = []
  // 国际产品线分类
  i18ProdGroupSort: Nullable<UcnEnVo> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 版本号
  version: Nullable<number> = null
  // 推荐防护
  recommendDetails: PackageProtectionDetailVo[] = []
  // 自选明细
  optionalDetails: PackageProtectionDetailVo[] = []
  // 区域客户设置
  regionDetails: PackageProtectionRegionDetailVo[] = []
  // 创建时间
  createTime: Nullable<Date> = null
}