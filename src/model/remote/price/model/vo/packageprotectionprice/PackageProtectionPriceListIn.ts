export default class PackageProtectionPriceListIn {
  // 运输模式等于
  modeEquals: Nullable<string> = null
  // 机型id等于
  prodModelIdEquals: Nullable<string> = null
  // 机型id所属一级国际产品线分类id等于。当 机型id等于 不为空时使用。前端不使用
  prodModelIdOwnerOneI18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 有效期开始日期起始于
  effectiveStartDateStart: Nullable<Date> = null
  // 有效期开始日期截至于
  effectiveStartDateEnd: Nullable<Date> = null
  // 有效期结束日期起始于
  effectiveEndDateStart: Nullable<Date> = null
  // 有效期结束日期截至于
  effectiveEndDateEnd: Nullable<Date> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
}