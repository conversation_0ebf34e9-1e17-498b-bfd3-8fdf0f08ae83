import PackageProtectionFormDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionFormDetailVo'
import UcnVo from 'model/remote/support/core/domain/UcnVo'

export default class PackageProtectionRegionDetailVo {
  // 大区编码
  regions: UcnVo[] = []
  // 细分区域编码
  regionDetails: UcnVo[] = []
  // 细分区域子公司id
  formSubsidiaries: UcnVo[] = []
  // 细分区域国家id
  formCountries: UcnVo[] = []
  // 推荐防护
  recommendDetails: PackageProtectionFormDetailVo[] = []
  // 自选防护
  optionalDetails: PackageProtectionFormDetailVo[] = []
}