import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import PackageProtectionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionDetailVo'
import PackageProtectionRegionDetailVo from 'model/remote/price/model/vo/packageprotectionprice/PackageProtectionRegionDetailVo'
import { TransportType } from 'model/remote/price/model/po/TransportType'

export default class PackageProtectionPriceListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 运输方式
  mode: Nullable<TransportType> = null
  // 运输方式名称
  modeName: Nullable<string> = null
  // 机型
  prodModels: string[] = []
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // en国际产品线分类
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 版本号
  version: Nullable<number> = null
  // 推荐防护
  recommendDetails: PackageProtectionDetailVo[] = []
  // 推荐防护Json
  recommendDetailsJson: Nullable<string> = null
  // 自选明细
  optionalDetails: PackageProtectionDetailVo[] = []
  // 自选明细Json
  optionalDetailsJson: Nullable<string> = null
  // 区域明细
  regionDetails: PackageProtectionRegionDetailVo[] = []
  // 区域明细json
  regionDetailsJson: Nullable<string> = null
}