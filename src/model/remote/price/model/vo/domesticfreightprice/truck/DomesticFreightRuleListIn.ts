import { CreateSource } from 'model/remote/price/model/po/CreateSource'

export default class DomesticFreightRuleListIn {
  // 计费终点类型
  billDestinationTypeEquals: Nullable<string> = null
  // 出发地等于
  domesticFreightLandCodeEquals: Nullable<string> = null
  // 起运站等于
  domesticFreightPortCodeEquals: Nullable<string> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id在...之中
  i18ProdGroupSortIdIn: string[] = []
  // 物流分组id等于
  logisticsGroupIdEquals: Nullable<string> = null
  // 机型Id等于
  prodModuleIdEquals: Nullable<string> = null
  // 数据来源等于
  ruleSourceEquals: Nullable<CreateSource> = null
  // 运费规则id模糊匹配
  idLike: Nullable<string> = null
  // 运费规则id在...范围内
  idIn: string[] = []
  // 有效期开始日期起始于
  effectiveStartDateStart: Nullable<Date> = null
  // 有效期开始日期截至于
  effectiveStartDateEnd: Nullable<Date> = null
  // 有效期结束日期起始于
  effectiveEndDateStart: Nullable<Date> = null
  // 有效期结束日期截至于
  effectiveEndDateEnd: Nullable<Date> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // 语言环境
  lang: Nullable<string> = null
}