import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { BillDestinationType } from 'model/remote/price/model/po/domesticfreightprice/truck/BillDestinationType'

export default class DomesticFreightRuleListOpenapiOut extends LtcEntityVo {
  // 运费规则id
  id: Nullable<string> = null
  // 出发地代码
  domesticFreightLandCode: Nullable<string> = null
  // 起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 计费终点类型
  billDestinationType: Nullable<BillDestinationType> = null
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // 国际产品线分类_英文
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 物流分组名称
  logisticsGroupName: Nullable<string> = null
  // 物流分组代码
  logisticsGroupId: Nullable<string> = null
  // 机型代码
  prodModuleCodes: string[] = []
  // 报价
  price: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 数据来源等于
  ruleSource: Nullable<string> = null
  // 备注
  note: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
}