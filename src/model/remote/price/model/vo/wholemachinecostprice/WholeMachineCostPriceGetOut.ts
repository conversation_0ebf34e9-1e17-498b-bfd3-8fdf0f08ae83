import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class WholeMachineCostPriceGetOut {
  // 价格记录id
  id: Nullable<string> = null
  // 物料号代码
  materialCode: Nullable<string> = null
  // 机型代码
  prodModelCode: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // 配置说明
  materialDesc: Nullable<string> = null
  // 成本价
  price: Nullable<number> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 版本号
  version: Nullable<number> = null
}