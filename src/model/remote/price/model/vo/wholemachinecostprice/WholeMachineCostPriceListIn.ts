export default class WholeMachineCostPriceListIn {
  // 物料号代码模糊匹配
  materialCodeLike: Nullable<string> = null
  // 机型代码模糊匹配
  prodModelCodeLike: Nullable<string> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id在...之中
  i18ProdGroupSortIdIn: string[] = []
  // 运费规则id模糊匹配
  idLike: Nullable<string> = null
  // 启用状态等于
  enabledEquals: Nullable<string> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
}