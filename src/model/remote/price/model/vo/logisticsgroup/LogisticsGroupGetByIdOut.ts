import UcnVo from 'model/remote/support/core/domain/UcnVo'

export default class LogisticsGroupGetByIdOut {
  // 唯一标识
  id: Nullable<string> = null
  // 物流分组名称
  logisticsGroupName: Nullable<string> = null
  // 机型
  prodModules: UcnVo[] = []
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // 国际产品线分类_英文
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
}