import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { FreightRuleSeason } from 'model/remote/price/model/po/FreightRuleSeason'

export default class SeasonBudgetExchangeRateListOut extends LtcEntityVo {
  // 唯一标识
  id: Nullable<string> = null
  // 源币种
  sourceCurrency: Nullable<string> = null
  // 目标币种
  targetCurrency: Nullable<string> = null
  // 源币种名称
  sourceCurrencyName: Nullable<string> = null
  // 目标币种名称
  targetCurrencyName: Nullable<string> = null
  // 汇率
  exchangeRate: Nullable<number> = null
  // 年份
  year: Nullable<string> = null
  // 季度
  season: Nullable<FreightRuleSeason> = null
}