import { FreightRuleSeason } from 'model/remote/price/model/po/FreightRuleSeason'

export default class SeasonBudgetExchangeRateListIn {
  // 源币种等于
  sourceCurrencyEquals: Nullable<string> = null
  // 目标币种等于
  targetCurrencyEquals: Nullable<string> = null
  // 年份等于
  yearEquals: Nullable<string> = null
  // 季度等于
  seasonEquals: Nullable<FreightRuleSeason> = null
  // 创建时间大于等于
  createTimeEqualsAndGenerateThan: Nullable<Date> = null
  // 创建时间小于等于
  createTimeEqualsAndLessThan: Nullable<Date> = null
}