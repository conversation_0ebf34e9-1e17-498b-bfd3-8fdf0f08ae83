import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class RelateSettlePriceListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 物料号
  materialCode: Nullable<string> = null
  // 超级bom标识，1-是，非固定物料，0-否，固定物料
  superBomFlag: Nullable<number> = null
  // 机型
  prodModelCode: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // en国际产品线分类
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 配置说明（营销）
  config: Nullable<string> = null
  // 配置说明（营销）_英文
  configEn: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // en子公司名称
  subsidiaryNameEn: Nullable<string> = null
  // 销售模式：买断、提佣
  saleMode: Nullable<string> = null
  // 结算价
  settlePrice: Nullable<number> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 版本号
  version: Nullable<number> = null
}