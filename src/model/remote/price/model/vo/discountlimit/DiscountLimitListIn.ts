export default class DiscountLimitListIn {
  // 使用年份等于
  yearEquals: Nullable<number> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 国际产品线分类id在...之中
  i18ProdGroupSortIdIn: string[] = []
  // 子公司id等于
  subsidiaryIdEquals: Nullable<number> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // 最新修改时间起始于
  lastModifiedStart: Nullable<Date> = null
  // 最新修改时间截止于
  lastModifiedEnd: Nullable<Date> = null
  // 初始额度为0,0-否,1-是
  initialLimitZero: Nullable<number> = null
}