import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class DiscountLimitListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 使用年份
  year: Nullable<number> = null
  // 国际产品线分类ID
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类名称
  i18ProdGroupSortName: Nullable<string> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 币种
  currency: Nullable<string> = null
  // 初始额度
  initialLimit: Nullable<number> = null
  // 总额度
  totalLimit: Nullable<number> = null
  // 预占额度
  preOccupiedLimit: Nullable<number> = null
  // 实际占用额度
  actualOccupiedLimit: Nullable<number> = null
  // 剩余额度
  remainLimit: Nullable<number> = null
  // 备注
  note: Nullable<string> = null
  // 创建人ID
  createUser: Nullable<string> = null
  // 创建人名称
  createUserName: Nullable<string> = null
  // 创建时间
  createTime: Nullable<Date> = null
  // 最后更新人ID
  lastModifier: Nullable<string> = null
  // 最后更新人名称
  lastModifierName: Nullable<string> = null
  // 最后更新时间
  lastModified: Nullable<Date> = null
}