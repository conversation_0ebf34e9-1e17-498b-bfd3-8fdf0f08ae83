import InternationalTransportInsuranceRateDetailVo from 'model/remote/price/model/vo/feerate/internationaltransportinsurancerate/InternationalTransportInsuranceRateDetailVo'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'

export default class InternationalTransportInsuranceRateGetOut extends LtcEntityVo {
  // 规则id
  id: Nullable<string> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 费率json
  ratesJson: Nullable<string> = null
  // 费率
  rates: InternationalTransportInsuranceRateDetailVo[] = []
  // 版本号
  version: Nullable<number> = null
}