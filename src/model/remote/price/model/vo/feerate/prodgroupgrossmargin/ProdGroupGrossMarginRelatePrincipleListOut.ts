import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class ProdGroupGrossMarginRelatePrincipleListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类
  i18ProdGroupSortName: Nullable<string> = null
  // en国际产品线分类
  i18ProdGroupSortNameEn: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // en子公司名称
  subsidiaryNameEn: Nullable<string> = null
  // 销售模式：买断、提佣
  saleMode: Nullable<string> = null
  // 子公司毛利率
  subsidiaryGrossMarginRate: Nullable<number> = null
  // 香港毛利率
  hongKongGrossMarginRate: Nullable<number> = null
  // 国际毛利率
  internationalGrossMarginRate: Nullable<number> = null
  // 工厂毛利率
  factoryGrossMarginRate: Nullable<number> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 版本号
  version: Nullable<number> = null
}