import CreditInsuranceRateDetailVo from 'model/remote/price/model/vo/feerate/creditinsurancerate/CreditInsuranceRateDetailVo'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import UcnVo from 'model/remote/support/core/domain/UcnVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class CreditInsuranceRateListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 国家分组id
  countryGroupId: Nullable<number> = null
  // 国家分组名称
  countryGroupName: Nullable<string> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 客户
  customs: UcnVo[] = []
  // 费率json
  ratesJson: Nullable<string> = null
  // 费率
  rates: CreditInsuranceRateDetailVo[] = []
  // 
  enabled: Nullable<EnabledState> = null
  // 启用状态
  version: Nullable<number> = null
  // 版本号
  effectiveStartDate: Nullable<Date> = null
  // 有效期开始日期
  effectiveEndDate: Nullable<Date> = null
  // 有效期结束日期
  createUser: Nullable<string> = null
  // 创建人ID
  createUserName: Nullable<string> = null
  // 创建人名称
  createTime: Nullable<Date> = null
}