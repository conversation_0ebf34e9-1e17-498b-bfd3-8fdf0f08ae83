import CreditInsuranceRateDetailVo from 'model/remote/price/model/vo/feerate/creditinsurancerate/CreditInsuranceRateDetailVo'

export default class CreditInsuranceRateSaveIn {
  // 国家分组ID
  countryGroupId: Nullable<number> = null
  // 客户ID
  customIds: number[] = []
  // 费率
  rates: CreditInsuranceRateDetailVo[] = []
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}