export default class ForwardDiscountRateListIn {
  // 子公司ID等于
  subsidiaryIdEquals: Nullable<number> = null
  // 办事处ID等于
  officeIdEquals: Nullable<number> = null
  // 国家ID等于
  countryIdEquals: Nullable<number> = null
  // 客户ID等于
  customIdEquals: Nullable<number> = null
  // 币种等于
  currencyCodeEquals: Nullable<string> = null
  // 有效期开始日期起始于
  effectiveStartDateStart: Nullable<Date> = null
  // 有效期开始日期截至于
  effectiveStartDateEnd: Nullable<Date> = null
  // 有效期结束日期起始于
  effectiveEndDateStart: Nullable<Date> = null
  // 有效期结束日期截至于
  effectiveEndDateEnd: Nullable<Date> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // 启用状态等于
  enabledEquals: Nullable<number> = null
  // 语言环境
  lang: Nullable<string> = null
  // 规则生效日期
  effectiveDate: Nullable<Date> = null
}