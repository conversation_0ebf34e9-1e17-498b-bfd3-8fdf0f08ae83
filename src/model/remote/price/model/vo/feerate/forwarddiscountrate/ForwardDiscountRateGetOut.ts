import ForwardDiscountRateDetailVo from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateDetailVo'

export default class ForwardDiscountRateGetOut {
  // id
  id: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // 办事处ID
  officeId: Nullable<number> = null
  // 办事处名称
  officeName: Nullable<string> = null
  // 国家ID
  countryId: Nullable<number> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 客户ID
  customId: Nullable<number> = null
  // 客户名称
  customName: Nullable<string> = null
  // 客户sap代码
  customNum: Nullable<string> = null
  // 币种
  currencyCode: Nullable<string> = null
  // 币种名称
  currencyName: Nullable<string> = null
  // 年利率
  rates: ForwardDiscountRateDetailVo[] = []
  // 年利率json
  ratesJson: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}