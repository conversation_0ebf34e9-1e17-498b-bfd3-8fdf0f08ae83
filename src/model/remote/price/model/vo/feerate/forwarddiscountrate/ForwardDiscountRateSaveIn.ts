import ForwardDiscountRateDetailVo from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateDetailVo'

export default class ForwardDiscountRateSaveIn {
  // id
  id: Nullable<string> = null
  // 子公司ID
  subsidiaryId: Nullable<number> = null
  // 办事处ID
  officeId: Nullable<number> = null
  // 国家ID
  countryId: Nullable<number> = null
  // 客户ID
  customId: Nullable<number> = null
  // 币种
  currencyCode: Nullable<string> = null
  // 年利率
  rates: ForwardDiscountRateDetailVo[] = []
  // 版本号
  version: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
}