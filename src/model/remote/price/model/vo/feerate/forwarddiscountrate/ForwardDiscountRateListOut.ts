import ForwardDiscountRateDetailVo from 'model/remote/price/model/vo/feerate/forwarddiscountrate/ForwardDiscountRateDetailVo'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class ForwardDiscountRateListOut extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 币种
  currencyCode: Nullable<string> = null
  // 子公司名称
  subsidiaryName: Nullable<string> = null
  // 办事处名称
  officeName: Nullable<string> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 客户名称
  customName: Nullable<string> = null
  // 客户Sap代码
  customNum: Nullable<string> = null
  // 币种名称
  currencyName: Nullable<string> = null
  // 年利率
  rates: ForwardDiscountRateDetailVo[] = []
  // 年利率json
  ratesJson: Nullable<string> = null
  // 启用状态
  enabled: Nullable<EnabledState> = null
  // 版本号
  version: Nullable<number> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 创建人ID
  createUser: Nullable<string> = null
  // 创建人名称
  createUserName: Nullable<string> = null
  // 创建时间
  createTime: Nullable<Date> = null
}