import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class DestinationPortListIn {
  // 编码模糊查询
  codeLike: Nullable<string> = null
  // 编码等于
  codeEquals: Nullable<string> = null
  // 中英文名称模糊查询
  nameLike: Nullable<string> = null
  // 英文名称模糊查询
  nameEnLike: Nullable<string> = null
  // 国家id等于
  countryEquals: Nullable<number> = null
  // 状态等于
  enabledEquals: Nullable<EnabledState> = null
}