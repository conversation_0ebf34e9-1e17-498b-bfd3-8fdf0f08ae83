import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { EnabledState } from 'model/remote/price/model/po/EnabledState'

export default class DestinationPortListOut extends LtcEntityVo {
  // 编码
  code: Nullable<string> = null
  // 中文名称
  name: Nullable<string> = null
  // 英文名称
  nameEn: Nullable<string> = null
  // 国家编码
  countryId: Nullable<number> = null
  // 国家代码
  countryCode: Nullable<string> = null
  // 国家名称
  countryName: Nullable<string> = null
  // 状态
  enabled: Nullable<EnabledState> = null
  // 备注
  note: Nullable<string> = null
}