import PaymentTypeGroupLineVo from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupLineVo'

export default class PaymentTypeGroupEditIn {
  // 唯一标识
  id: Nullable<string> = null
  // 中文名称
  name: Nullable<string> = null
  // 英文名称
  nameEn: Nullable<string> = null
  // 付款方式数量
  lineQuantity: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
  // 付款明细
  lines: PaymentTypeGroupLineVo[] = []
}