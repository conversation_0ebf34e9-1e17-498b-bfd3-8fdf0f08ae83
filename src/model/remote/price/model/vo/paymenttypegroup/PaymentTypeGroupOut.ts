import PaymentTypeGroupLineVo from 'model/remote/price/model/vo/paymenttypegroup/PaymentTypeGroupLineVo'

export default class PaymentTypeGroupOut {
  // 唯一标识
  id: Nullable<string> = null
  // 中文名称
  name: Nullable<string> = null
  // 英文名称
  nameEn: Nullable<string> = null
  // 付款方式数量
  lineQuantity: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
  // 创建人ID
  createUser: Nullable<string> = null
  // 创建人名称
  createUserName: Nullable<string> = null
  // 创建时间
  createTime: Nullable<Date> = null
  // 最后更新人ID
  lastModifier: Nullable<string> = null
  // 最后更新人名称
  lastModifierName: Nullable<string> = null
  // 最后更新时间
  lastModified: Nullable<Date> = null
  // 付款明细
  lines: PaymentTypeGroupLineVo[] = []
}