export default class YearGuidePriceMatchResult {
  // 
  materialCode: Nullable<string> = null
  // 
  exwPrice: Nullable<number> = null
  // 
  exwPriceCurrencyCode: Nullable<string> = null
  // 
  exwPriceId: Nullable<string> = null
  // 
  specialPrice: Nullable<number> = null
  // 
  specialPriceCurrencyCode: Nullable<string> = null
  // 
  specialPriceId: Nullable<string> = null
  // 
  limitMat: Nullable<boolean> = null
  // 
  limitQty: Nullable<number> = null
  // 
  useQty: Nullable<number> = null
  // 
  specialPriceMaxQty: Nullable<number> = null
  // 
  discount: Nullable<boolean> = null
  // 
  hasSpecialPrice: Nullable<boolean> = null
  // 
  noMatchedPrice: Nullable<boolean> = null
}