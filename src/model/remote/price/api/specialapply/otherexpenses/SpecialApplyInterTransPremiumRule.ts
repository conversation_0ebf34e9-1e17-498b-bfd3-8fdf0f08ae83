import SpecialApplyCtryRate from 'model/remote/price/api/specialapply/otherexpenses/SpecialApplyCtryRate'
import { InterTransPremiumRuleType } from 'model/remote/price/api/quotationapply/bill/InterTransPremiumRuleType'

// 特价申请单国际运输保险费规则
export default class SpecialApplyInterTransPremiumRule {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  owner: Nullable<string> = null
  // 加价系数
  advanceRate: Nullable<number> = null
  // 国家费率。前端当前不展示
  ctryRate: SpecialApplyCtryRate[] = []
  // 类型-舱内、舱外。默认舱外
  type: Nullable<InterTransPremiumRuleType> = null
}