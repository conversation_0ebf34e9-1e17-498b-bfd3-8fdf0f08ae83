import ExchangeRate from 'model/remote/price/api/profitanalysis/ExchangeRate'
import SpecialApplySelfBuyProfitAnalysis from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplySelfBuyProfitAnalysis'
import SpecialApplySelfCommissionExwProfitAnalysis from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplySelfCommissionExwProfitAnalysis'
import SpecialApplySupplyProfitAnalysis from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplySupplyProfitAnalysis'
import { SpecialApplyProfitAnalysisType } from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysisType'

// 特价申请计算毛利分析
export default class BSpecialApplyProfitAnalysis {
  // id
  id: Nullable<string> = null
  // 归属单号
  owner: Nullable<string> = null
  // 毛利分析类型
  profitAnalysisType: Nullable<SpecialApplyProfitAnalysisType> = null
  // 合同涉及金额汇率
  exchangeRateList: ExchangeRate[] = []
  // 毛利分析数据-自营提用exw
  selfCommissionExw: Nullable<SpecialApplySelfCommissionExwProfitAnalysis> = null
  // 年度定价数据-毛利分析数据-自营提用exw
  annualPricingSelfCommissionExw: Nullable<SpecialApplySelfCommissionExwProfitAnalysis> = null
  // 毛利分析数据-自营买断
  selfBuy: Nullable<SpecialApplySelfBuyProfitAnalysis> = null
  // 年度定价数据-毛利分析数据-自营买断
  annualPricingSelfBuy: Nullable<SpecialApplySelfBuyProfitAnalysis> = null
  // 毛利分析数据-供货
  supply: Nullable<SpecialApplySupplyProfitAnalysis> = null
  // 年度定价数据-毛利分析数据-供货
  annualPricingSupply: Nullable<SpecialApplySupplyProfitAnalysis> = null
}