import SpecialApplyProfitAnalysissExchangeRateLine from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysissExchangeRateLine'

// 特价毛利分析-查询请求
export default class SpecialApplyProfitAnalysisQueryRequest {
  // 单号
  billNum: Nullable<string> = null
  // 特价申请物料价格总览(物料指导价维度)
  specialApplyMatOverviewId: Nullable<string> = null
  // 利率类型： bill("单据汇率")   annualBudget("年度预算汇率") now("最新汇率")
  rateType: Nullable<SpecialApplyProfitAnalysissExchangeRateLine> = null
}