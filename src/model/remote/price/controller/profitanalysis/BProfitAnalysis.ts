import ContractQuotationSelfCommissionExwProfitAnalysis from 'model/remote/price/api/profitanalysis/contractquotation/ContractQuotationSelfCommissionExwProfitAnalysis'
import ContractQuotationSelfCommissionUnExwProfitAnalysis from 'model/remote/price/api/profitanalysis/contractquotation/ContractQuotationSelfCommissionUnExwProfitAnalysis'
import ContractQuotationSupplyProfitAnalysis from 'model/remote/price/api/profitanalysis/contractquotation/ContractQuotationSupplyProfitAnalysis'
import ExchangeRate from 'model/remote/price/api/profitanalysis/ExchangeRate'
import QuotationApplyProfitAnalysisMatLine from 'model/remote/price/api/quotationapply/bill/QuotationApplyProfitAnalysisMatLine'
import { BillType } from 'model/remote/price/api/approvenode/BillType'
import { MatProfitAnalysisType } from 'model/remote/price/api/profitanalysis/MatProfitAnalysisType'

// 毛利分析
export default class BProfitAnalysis {
  // id
  id: Nullable<string> = null
  // 归属单号
  owner: Nullable<string> = null
  //  物料号
  matCd: Nullable<string> = null
  // 报价单物料
  quotationMat: Nullable<QuotationApplyProfitAnalysisMatLine> = null
  // 毛利分析单据类型
  billType: Nullable<BillType> = null
  // 毛利分析类型
  profitAnalysisType: Nullable<MatProfitAnalysisType> = null
  // 合同涉及金额汇率
  exchangeRateList: ExchangeRate[] = []
  // 毛利分析数据-自营提用exw
  selfCommissionExw: Nullable<ContractQuotationSelfCommissionExwProfitAnalysis> = null
  // 年度定价数据-自营提用exw
  annualPricingSelfCommissionExw: Nullable<ContractQuotationSelfCommissionExwProfitAnalysis> = null
  // 毛利分析数据-自营提用非exw
  selfCommissionUnExw: Nullable<ContractQuotationSelfCommissionUnExwProfitAnalysis> = null
  // 年度定价数据-自营提用非exw
  annualPricingSelfCommissionUnExw: Nullable<ContractQuotationSelfCommissionUnExwProfitAnalysis> = null
  // 毛利分析数据-供货
  supply: Nullable<ContractQuotationSupplyProfitAnalysis> = null
  // 年度定价数据-自营提用exw
  annualPricingSupply: Nullable<ContractQuotationSupplyProfitAnalysis> = null
}