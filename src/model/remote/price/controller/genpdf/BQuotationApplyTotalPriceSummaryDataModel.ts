import BQuotationApplyBaseDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyBaseDataModel'
import BQuotationApplyTotalPricePaymentLine from 'model/remote/price/controller/genpdf/BQuotationApplyTotalPricePaymentLine'

// 导出报价申请总价页Summary变量
export default class BQuotationApplyTotalPriceSummaryDataModel extends BQuotationApplyBaseDataModel {
  // 后端固定从oss路径（ltc/pdf/logoImg.jpg）下载后转换，前端忽略
  logoImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/bgImg.png）下载后转换，前端忽略
  bgImg: Nullable<string> = null
  // 累加【物料页】全部物料的「Machine Price」之和。包含币种符号
  machinePrice: Nullable<string> = null
  // 整单物流实际费用。包含币种符号
  logisticsCost: Nullable<string> = null
  // 整单其他费用-整单延保费。包含币种符号
  otherCost: Nullable<string> = null
  // 整单延保费。包含币种符号
  extendedWarranty: Nullable<string> = null
  // 报价总金额，无折扣时，此值也就等于实际报价总金额。包含币种符号
  total: Nullable<string> = null
  // total-totalWOrDiscount，≥0则构造为空字符串（无需传值）。包含币种符号
  totalDiscount: Nullable<string> = null
  // 实际报价总金额，与totalDiscount是否展示绑定，如果totalDiscount≥0则构造为空字符串（无需传值）。包含币种符号
  totalWOrDiscount: Nullable<string> = null
  // 付款方式行。数组
  paymentLines: BQuotationApplyTotalPricePaymentLine[] = []
}