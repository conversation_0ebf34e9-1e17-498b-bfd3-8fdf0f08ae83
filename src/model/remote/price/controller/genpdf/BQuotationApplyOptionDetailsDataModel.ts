import BQuotationApplyBaseDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyBaseDataModel'
import OptionLine from 'model/remote/price/controller/genpdf/OptionLine'

// 导出报价申请配置明细页或配置明细页(含价格)变量
export default class BQuotationApplyOptionDetailsDataModel extends BQuotationApplyBaseDataModel {
  // 后端固定从oss路径（ltc/pdf/logoImg.jpg）下载后转换，前端忽略
  logoImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/bgImg.png）下载后转换，前端忽略
  bgImg: Nullable<string> = null
  // 机型id。用于从cpq获取机型图片.可用数据示例：100098、100155、100090前端注意，实际的ftl模板无此字段。前端需要传值
  prodMdlId: Nullable<string> = null
  // 机型图片，后端根据机型id下载后转换。前端无需传值
  prodMdlImg: Nullable<string> = null
  // 国际产品线名称英文拼接机型cd
  i18ProdGroupNameEnProdMdlCd: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 整机延保政策英文名称。整机延保政策为空则构造空字符串
  policyNameEnForTotal: Nullable<string> = null
  // 左边配置明细。非空数组。已按照标题类型排序
  leftOptionLines: OptionLine[] = []
  // 右边配置明细。可为空数组，为空时不需要展示分栏样式。已按照标题类型排序
  rightOptionLines: OptionLine[] = []
}