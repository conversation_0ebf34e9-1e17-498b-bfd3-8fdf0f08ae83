import BQuotationApplyBaseDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyBaseDataModel'

// 导出报价申请首页变量
export default class BQuotationApplyHomeDataModel extends BQuotationApplyBaseDataModel {
  // 国际产品线名称英文拼接机型cd。如果多个产品线机型则构造为空字符串
  i18ProdGroupNameEnProdMdlCd: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/bgWatermarkImg.png）下载后转换，前端忽略
  bgWatermarkImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/homeImg.png）下载后转换，前端忽略
  homeImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/logoImg.jpg）下载后转换，前端忽略
  logoImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/emailImg.png）下载后转换，前端忽略
  emailImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/mobileImg.png）下载后转换，前端忽略
  mobileImg: Nullable<string> = null
}