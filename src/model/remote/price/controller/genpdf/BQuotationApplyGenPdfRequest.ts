// 导出报价申请请求
export default class BQuotationApplyGenPdfRequest {
  // 报价申请单号。必填
  id: Nullable<string> = null
  // 信息组：cover-封面;totalPrice-总价页;optionDetails-配置明细页;optionDetailsPrice-配置明细页(含价格);otherFeeDetails-其他费用明细页。必填
  fields: string[] = []
  // 柳工联系人-姓名。下载必填
  lgFullName: Nullable<string> = null
  // 柳工联系人-邮箱。下载必填
  lgEmail: Nullable<string> = null
  // 柳工联系人-电话。下载必填
  lgMobileNumber: Nullable<string> = null
  // 经销商联系人-姓名。下载选填。买断场景下可不填
  dlrFullName: Nullable<string> = null
  // 经销商联系人-邮箱。下载选填。买断场景下可不填
  dlrEmail: Nullable<string> = null
  // 经销商联系人-电话。下载选填。买断场景下可不填
  dlrMobileNumber: Nullable<string> = null
  // 导出语言，中文:zh;英文:en。可为空。为空时取请求头Accept-Language对应的语种
  exportLanguage: Nullable<string> = null
}