export default class OptionLine {
  // 标题类型。group-分组（即固定配置、可选、必选、TOTAL，构造为全大写英文，对应一级标题）；category-分类（对应二级标题）；optionValue-选项值（构造时前面需加点隔开，对应三级标题）
  type: Nullable<string> = null
  // 配置名称英文。最少占用一行，对于分组、分类仅预估占用一行;对于选项值，根据字数预估占用一行或多行
  optionNameEn: Nullable<string> = null
  // 分组价格或分类价格。包含币种符号。可为空，为空则构造空字符串，前端不展示。当配置明细不含价格时，一定为空；当配置明细含价格时，标题类型为group或category时可能为空；标题类型为optionValue时一定为空
  price: Nullable<string> = null
  // category-分类下的选项值明细，在构造分页分栏时使用，前端无需关注
  categoryLines: OptionLine[] = []
  // category-分类所属的分组选项值，在构造分页分栏时使用，前端无需关注
  categoryGroup: Nullable<OptionLine> = null
}