import BQuotationApplyBaseDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyBaseDataModel'
import BQuotationApplyCostLine from 'model/remote/price/controller/genpdf/BQuotationApplyCostLine'

// 导出报价申请其他费用物料页变量
export default class BQuotationApplyOtherFeeMaterialDataModel extends BQuotationApplyBaseDataModel {
  // 后端固定从oss路径（ltc/pdf/logoImg.jpg）下载后转换，前端忽略
  logoImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/bgImg.png）下载后转换，前端忽略
  bgImg: Nullable<string> = null
  // 机型id。用于从cpq获取机型图片.可用数据示例：100098、100155、100090前端注意，实际的ftl模板无此字段。前端需要传值
  prodMdlId: Nullable<string> = null
  // 机型图片，后端根据机型id下载后转换。前端无需传值
  prodMdlImg: Nullable<string> = null
  // 国际产品线名称英文拼接机型cd
  i18ProdGroupNameEnProdMdlCd: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 台量。包含乘号
  qty: Nullable<string> = null
  // 整机延保政策英文名称。整机延保政策为空则构造空字符串
  policyNameEnForTotal: Nullable<string> = null
  // 控制物料价格内容（除物料延保费，包含物料总计、物料小计、物料物流实际费用小计、物料物流费用明细行、物料其他费用、物料其他费用明细行）是否展示:true-不展示;false-展示。默认展示
  priceShow: Nullable<boolean> = null
  // 物料总计，报价总金额，无折扣时，此值也就等于实际报价总金额。包含币种符号
  total: Nullable<string> = null
  // 物料小计。包含币种符号
  machinePrice: Nullable<string> = null
  // 物料物流实际费用小计。包含币种符号
  logisticsCost: Nullable<string> = null
  // 物料物流费用明细行。数组。可能为空。如果费用项金额为0，则数组不包含该明细行
  logisticsCostLines: BQuotationApplyCostLine[] = []
  // 控制物料其他费用内容(其他费用、Total)是否展示:true-不展示;false-展示。默认展示
  otherCostShow: Nullable<boolean> = null
  // 物料其他费用-整单延保费小计。包含币种符号
  otherCost: Nullable<string> = null
  // 物料其他费用明细行。数组。可能为空。如果费用项金额为0，则数组不包含该明细行
  otherCostLines: BQuotationApplyCostLine[] = []
  // 控制物料延保费内容（包含物料延保费小计、物料延保费明细行）是否展示:true-不展示;false-展示。默认展示
  extendedWarrantyShow: Nullable<boolean> = null
  // 物料延保费小计。包含币种符号
  extendedWarranty: Nullable<string> = null
  // 物料延保费明细行。数组。可能为空。如果费用项金额为0，则数组不包含该明细行
  extendedWarrantyLines: BQuotationApplyCostLine[] = []
  // 控制additional内容是否展示:true-不展示;false-展示。默认展示
  additionalShow: Nullable<boolean> = null
  // 自营-贸易术语，来自数据字典。当为供货时，构造为空字符串
  incotermsId: Nullable<string> = null
  // 自营-目的港英文名称。当为供货时，构造为空字符串
  destinationPortNameEn: Nullable<string> = null
  // 自营-国际运输模式名称英文。当为供货时，构造为空字符串
  transportTypeNameEn: Nullable<string> = null
  // 供货-交货地点。当为自营时，构造为空字符串
  domesticFreightLandNameEn: Nullable<string> = null
  // 供货-国内运输模式名称英文。当为自营时，构造为空字符串
  dmTransportTypeNameEn: Nullable<string> = null
  // 预计签署日期: dd/MM/yyyy
  signDate: Nullable<string> = null
  // 预计发运日期: dd/MM/yyyy
  deliveryDate: Nullable<string> = null
}