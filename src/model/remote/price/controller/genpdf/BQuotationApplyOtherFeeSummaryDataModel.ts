import BQuotationApplyBaseDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyBaseDataModel'
import BQuotationApplyCostLine from 'model/remote/price/controller/genpdf/BQuotationApplyCostLine'

// 导出报价申请其他费用Summary页变量
export default class BQuotationApplyOtherFeeSummaryDataModel extends BQuotationApplyBaseDataModel {
  // 后端固定从oss路径（ltc/pdf/logoImg.jpg）下载后转换，前端忽略
  logoImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/bgImg.png）下载后转换，前端忽略
  bgImg: Nullable<string> = null
  // 累加【物料页】全部物料的「Machine Price」之和。包含币种符号
  machinePrice: Nullable<string> = null
  // 整单物流实际费用。包含币种符号
  logisticsCost: Nullable<string> = null
  // 物流费用明细行。数组。可能为空。如果费用项金额为0，则数组不包含该明细行
  logisticsCostLines: BQuotationApplyCostLine[] = []
  // 整单其他费用-整单延保费。包含币种符号
  otherCost: Nullable<string> = null
  // 其他费用明细行。数组。可能为空。如果费用项金额为0，则数组不包含该明细行
  otherCostLines: BQuotationApplyCostLine[] = []
  // 整单延保费。包含币种符号。非空。如果没有延保明细，将构造为0，两行均展示此值
  extendedWarranty: Nullable<string> = null
  // 报价总金额，无折扣时，此值也就等于实际报价总金额。包含币种符号
  total: Nullable<string> = null
  // total-totalWOrDiscount，≥0则构造为空字符串（无需传值）。包含币种符号
  totalDiscount: Nullable<string> = null
  // 实际报价总金额，与totalDiscount是否展示绑定，如果totalDiscount≥0则构造为空字符串（无需传值）。包含币种符号
  totalWOrDiscount: Nullable<string> = null
  // 自营-贸易术语，来自数据字典。当为供货时，构造为空字符串
  incotermsId: Nullable<string> = null
  // 自营-目的港英文名称。当为供货时，构造为空字符串
  destinationPortNameEn: Nullable<string> = null
  // 自营-国际运输模式名称英文。当为供货时，构造为空字符串
  transportTypeNameEn: Nullable<string> = null
  // 供货-交货地点。当为自营时，构造为空字符串
  domesticFreightLandNameEn: Nullable<string> = null
  // 供货-国内运输模式名称英文。当为自营时，构造为空字符串
  dmTransportTypeNameEn: Nullable<string> = null
  // 预计签署日期: dd/MM/yyyy
  signDate: Nullable<string> = null
  // 预计发运日期: dd/MM/yyyy
  deliveryDate: Nullable<string> = null
}