import BQuotationApplyBaseDataModel from 'model/remote/price/controller/genpdf/BQuotationApplyBaseDataModel'
import BQuotationApplyTotalPricePaymentLine from 'model/remote/price/controller/genpdf/BQuotationApplyTotalPricePaymentLine'

// 导出报价申请总价页物料页变量
export default class BQuotationApplyTotalPriceMaterialDataModel extends BQuotationApplyBaseDataModel {
  // 后端固定从oss路径（ltc/pdf/logoImg.jpg）下载后转换，前端忽略
  logoImg: Nullable<string> = null
  // 后端固定从oss路径（ltc/pdf/bgImg.png）下载后转换，前端忽略
  bgImg: Nullable<string> = null
  // 机型id。用于从cpq获取机型图片.可用数据示例：100098、100155、100090前端注意，实际的ftl模板无此字段。前端需要传值
  prodMdlId: Nullable<string> = null
  // 机型图片，后端根据机型id下载后转换。前端无需传值
  prodMdlImg: Nullable<string> = null
  // 国际产品线名称英文拼接机型cd
  i18ProdGroupNameEnProdMdlCd: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 台量。包含乘号
  qty: Nullable<string> = null
  // 单台Machine Price。该物料设备无折扣时，展示【设备报价】（溢价之后的逻辑价格），单台价格;该物料设备有折扣时，展示【年度指导价】，单台价格。包含币种符号
  singleMachinePrice: Nullable<string> = null
  // 物料总计，报价总金额，无折扣时，此值也就等于实际报价总金额。包含币种符号
  total: Nullable<string> = null
  // 物料singleMachinePrice小计。包含币种符号
  machinePrice: Nullable<string> = null
  // 物料物流实际费用小计。包含币种符号
  logisticsCost: Nullable<string> = null
  // 物料其他费用-整单延保费小计。包含币种符号
  otherCost: Nullable<string> = null
  // 物料延保费小计。包含币种符号
  extendedWarranty: Nullable<string> = null
  // 付款方式行。数组
  paymentLines: BQuotationApplyTotalPricePaymentLine[] = []
}