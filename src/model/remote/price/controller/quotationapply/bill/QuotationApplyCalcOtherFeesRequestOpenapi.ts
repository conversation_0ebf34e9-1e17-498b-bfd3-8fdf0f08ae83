import MatFeeOpenapi from 'model/remote/price/controller/quotationapply/logisticscost/MatFeeOpenapi'
import PackPlanVo from 'model/remote/price/api/quotationapply/calpackplan/PackPlanVo'
import ProtectionSchemeVo from 'model/remote/price/api/quotationapply/protection/ProtectionSchemeVo'
import QuotationApplyMatLineOtherFeesOpenapi from 'model/remote/price/controller/quotationapply/QuotationApplyMatLineOtherFeesOpenapi'
import QuotationApplyPayLineOpenapi from 'model/remote/price/controller/quotationapply/other/QuotationApplyPayLineOpenapi'
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType'
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType'

// 报价申请单其他费用对外
export default class QuotationApplyCalcOtherFeesRequestOpenapi {
  // 出口类型
  exportType: Nullable<ExportType> = null
  // 销售模式，exportType=self时使用
  saleMode: Nullable<GuidePriceType> = null
  // 办事处ID
  ofcId: string = ''
  // 子公司ID
  sbsdyId: string = ''
  // 销往国ID
  ctryId: string = ''
  // 币种id，来自数据字典
  currencyId: string = ''
  // 主题
  topic: Nullable<string> = null
  // 客户ID
  custId: Nullable<string> = null
  // 预计签署日期
  signDate: Nullable<string> = null
  // 是否加急审批
  urgentApprove: Nullable<boolean> = null
  // 预计发货日期
  deliveryDate: Nullable<string> = null
  // 是否需要柳工工厂内装
  liugongPrePackage: Nullable<boolean> = null
  // 是否需要防护
  needProtected: Nullable<boolean> = null
  // 自营-贸易术语，来自数据字典
  incotermsId: Nullable<string> = null
  // 自营-国际运输模式，来自数据字典
  transportTypeId: Nullable<string> = null
  // 自营-国际运输模式类型-滚装、散杂，来自数据字典
  transportTypeRoleId: Nullable<string> = null
  // 自营-目的港代码
  destinationPortCode: Nullable<string> = null
  // 供货-交货地点
  domesticFreightLandCode: Nullable<string> = null
  // 供货-柳工是否收取装箱费：liugongPrePackage=true时给值
  liugongChargePackagePrice: Nullable<boolean> = null
  // 物料明细及其他费用、延保等
  matPriceLines: QuotationApplyMatLineOtherFeesOpenapi[] = []
  // 装运方案
  packPlanVo: Nullable<PackPlanVo> = null
  // 防护方案
  protectionSchemeVo: Nullable<ProtectionSchemeVo> = null
  // 物流费用
  logisticsFees: MatFeeOpenapi[] = []
  // 付款明细
  payLines: QuotationApplyPayLineOpenapi[] = []
}