import PageQueryRequest from 'model/remote/support/common/PageQueryRequest'

// 报价申请单查询过滤器
export default class BQuotationApplyBillFilter extends PageQueryRequest {
  // 主题 类似于
  topicLike: Nullable<string> = null
  // 单号 起始于
  idStart: Nullable<string> = null
  // 外部单号 起始于
  billNumberStart: Nullable<string> = null
  // 数据来源 等于
  dataSourceEq: Nullable<string> = null
  // 创建人ID 等于
  createUseridEq: Nullable<string> = null
  // 客户ID 等于
  custIdEq: Nullable<string> = null
  // 状态 等于
  stateEq: Nullable<string> = null
  // 审批节点ID 等于
  approveNodeIdEq: Nullable<string> = null
  // 报价合同类型 等于
  contractTypeEq: Nullable<string> = null
  // 贸易术语ID 等于
  incotermsIdEq: Nullable<string> = null
  // 出口类型ID 等于
  exportTypeIdEq: Nullable<string> = null
  // 机型ID in
  prodMdlIdIn: string[] = []
  // 机型代码 in
  prodMdlCdIn: string[] = []
  // 关联合同 等于
  referContractEq: Nullable<string> = null
  // 子公司ID 等于
  sbsdyIdEq: Nullable<string> = null
  // 办事处ID 等于
  ofcIdEq: Nullable<string> = null
  // 销往国ID 等于
  ctryIdEq: Nullable<string> = null
  // 是否加急审批 等于
  urgentApproveEq: Nullable<boolean> = null
  // 创建时间 起始
  createTimeStart: Nullable<Date> = null
  // 创建时间 截止
  createTimeEnd: Nullable<Date> = null
  // 审批节点 等于
  processNodeNameEq: Nullable<string> = null
}