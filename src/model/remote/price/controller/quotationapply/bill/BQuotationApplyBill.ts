import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'
import { DataSource } from 'model/remote/price/model/po/DataSource'
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType'
import { PackingPlanRecommendType } from 'model/remote/price/api/quotationapply/bill/PackingPlanRecommendType'
import { QuotationApplyState } from 'model/remote/price/api/quotationapply/bill/QuotationApplyState'
import { QuotationContractType } from 'model/remote/price/api/quotationapply/bill/QuotationContractType'

// 报价申请单
export default class BQuotationApplyBill extends LtcEntityVo {
  // ID,存储单号
  id: Nullable<string> = null
  // 所属组织id
  orgId: Nullable<string> = null
  // 外部单号
  billNumber: Nullable<string> = null
  // 数据来源
  dataSource: Nullable<DataSource> = null
  // 主题
  topic: Nullable<string> = null
  // 客户ID
  custId: Nullable<string> = null
  // 客户代码
  custCode: Nullable<string> = null
  // 客户名称
  custName: Nullable<string> = null
  // 办事处ID
  ofcId: Nullable<string> = null
  // 办事处名称
  ofcName: Nullable<string> = null
  // 办事处名称(英文)
  ofcNameEn: Nullable<string> = null
  // 子公司ID
  sbsdyId: Nullable<string> = null
  // 子公司名称
  sbsdyName: Nullable<string> = null
  // 子公司名称(英文)
  sbsdyNameEn: Nullable<string> = null
  // 销往国ID
  ctryId: Nullable<string> = null
  // 销往国名称
  ctryName: Nullable<string> = null
  // 销售模式，出口类型是自营时，贸易术语不能为空
  saleMode: Nullable<GuidePriceType> = null
  // 贸易术语，来自数据字典，出口类型是自营时，贸易术语不能为空
  incotermsId: Nullable<string> = null
  // 币种id，来自数据字典
  currencyId: Nullable<string> = null
  // 币种名称，来自数据字典
  currencyName: Nullable<string> = null
  // 币种符号，来自数据字典
  currencySymbol: Nullable<string> = null
  // 合同币种折CNY汇率
  cnyExRate: Nullable<number> = null
  // 合同币种折USD汇率
  usdExRate: Nullable<number> = null
  // CNY折合同币种汇率
  cnyExCurrencyRate: Nullable<number> = null
  // USD折合同币种汇率
  usdExCurrencyRate: Nullable<number> = null
  // 预计签署日期
  signDate: Nullable<string> = null
  // 报价合同类型
  contractType: Nullable<QuotationContractType> = null
  // 关联合同
  referContracts: string[] = []
  // 物料台量合计
  qtyTotal: Nullable<number> = null
  // 出口类型，来自数据字典
  exportTypeId: Nullable<string> = null
  // 是否加急审批
  urgentApprove: Nullable<boolean> = null
  // 状态
  state: Nullable<QuotationApplyState> = null
  // 是否潜客
  potentialCus: Nullable<boolean> = null
  // 审批流程ID
  approveProcessId: Nullable<string> = null
  // 预计发货日期
  deliveryDate: Nullable<string> = null
  // 目的港代码
  destinationPortCode: Nullable<string> = null
  // 目的港名称
  destinationPortName: Nullable<string> = null
  // 国际运输模式，来自数据字典
  transportTypeId: Nullable<string> = null
  // 国际运输模式类型-集装箱、滚装、散杂，来自数据字典
  transportTypeRoleId: Nullable<string> = null
  // 是否需要柳工工厂内装
  liugongPrePackage: Nullable<boolean> = null
  // 默认起运港代码
  domesticFreightPortCode: Nullable<string> = null
  // 默认起运港名称
  domesticFreightPortName: Nullable<string> = null
  // 装运方案类型-系统推荐方案、物流定制方案、物流询价
  packingPlanType: Nullable<PackingPlanRecommendType> = null
  // 物流回写是否拼箱 true-拼箱
  qtPacking: Nullable<boolean> = null
  // 物流定制是否完成：true-已完成；false:未完成
  logisticsDesignFinished: Nullable<boolean> = null
  // 物流询价是否完成：true-已完成；false:未完成
  logisticsInquiryFinished: Nullable<boolean> = null
  // 提佣子公司ID
  commissionSbsdyId: Nullable<string> = null
  // 整单子公司佣金合计
  commissionSbsdyAmount: Nullable<number> = null
  // 折扣率
  discountRate: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
  // 任务节点状态名称。
  taskState: Nullable<string> = null
  // 任务节点状态代码。
  taskStateCode: Nullable<string> = null
  // 任务出口。
  userTaskOutGoings: UserTaskOutGoing[] = []
  // 是否需要防护
  needProtected: Nullable<boolean> = null
  // 特殊信保费率申请原因
  applyReason: Nullable<string> = null
  // 供货-交货地点 代码
  domesticFreightLandCode: Nullable<string> = null
  // 供货-交货地点 名称
  domesticFreightLandName: Nullable<string> = null
  // 供货-柳工是否收取装箱费：liugongPrePackage=true时给值
  liugongChargePackagePrice: Nullable<boolean> = null
  // 供货-国内运输模式
  dmTransportTypeId: Nullable<string> = null
  // 供货-国内运输保险费是否投保
  domesticTransInsurancePremium: Nullable<boolean> = null
  // 目的港/目的站所属国家代码
  destinationPortCtryCd: Nullable<string> = null
  // 来源单号
  sourceBillNo: Nullable<string> = null
  // 是否缺失费用规则
  missRule: Nullable<boolean> = null
  // 版本，用于区分 物流国内部分费用是【按柜型计v1】还是【按机型计v2】
  edition: Nullable<string> = null
}