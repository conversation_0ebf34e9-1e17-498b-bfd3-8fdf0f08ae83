import BYearGuidePriceMatchResult from 'model/remote/price/controller/yearguideprice/yearguideprice/BYearGuidePriceMatchResult'
import OptionDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/OptionDetailsSpecialConfigVo'
import StaticConfigDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/StaticConfigDetailsSpecialConfigVo'
import StringIdName from 'model/remote/support/core/domain/StringIdName'
import { DataSource } from 'model/remote/price/model/po/DataSource'

// 报价申请单物料明细
export default class BQuotationApplyMatLine {
  // ID
  id: Nullable<string> = null
  // 数据来源
  dataSource: Nullable<DataSource> = null
  // 所属报价申请单, QuotationApplyBill.id
  owner: Nullable<string> = null
  // 行号，需前端传入，比如001、002
  line: Nullable<string> = null
  // 拼箱行号
  packLine: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 指导价
  exwPrice: Nullable<number> = null
  // 设备报价(折扣后)
  actualPrice: Nullable<number> = null
  // 是否使用特价
  useSpecial: Nullable<boolean> = null
  // 台量
  qty: Nullable<number> = null
  // 总价
  total: Nullable<number> = null
  // 是否拆解
  decompose: Nullable<boolean> = null
  // 预算体积
  budgetVolume: Nullable<number> = null
  // 物流定制行号
  designLine: Nullable<number> = null
  // 固定配置明细（带特殊配置）
  staticConfigDetails: StaticConfigDetailsSpecialConfigVo[] = []
  // 选项明细（带特殊配置）
  optionDetails: OptionDetailsSpecialConfigVo[] = []
  // 特殊配置key，由特殊配置id构成，按照正序排列，用@@拼接
  specialConfigKey: Nullable<string> = null
  // 特殊配置明细
  specialConfigs: StringIdName[] = []
  // 属具数量
  accessoryQty: Nullable<number> = null
  // 年度指导价计算结果
  guidePrice: Nullable<BYearGuidePriceMatchResult> = null
  // 成本价（CNY）
  costPrice: Nullable<number> = null
}