import PackPlanVo from 'model/remote/price/api/quotationapply/calpackplan/PackPlanVo'
import ProtectionSchemeVoV2 from 'model/remote/price/api/quotationapply/protectionv2/ProtectionSchemeVoV2'
import QuotationApplyCalcPlanRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcPlanRequestOpenapi'

// 报价申请单物流费用对外
export default class QuotationApplyCalcLogisticsFeesRequestOpenapiV2 extends QuotationApplyCalcPlanRequestOpenapi {
  // 装运方案
  packPlanVo: Nullable<PackPlanVo> = null
  // 防护方案
  protectionSchemeVo: Nullable<ProtectionSchemeVoV2> = null
}