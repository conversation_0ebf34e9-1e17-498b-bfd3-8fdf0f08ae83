import PackPlanVo from 'model/remote/price/api/quotationapply/calpackplan/PackPlanVo'
import ProtectionSchemeVo from 'model/remote/price/api/quotationapply/protection/ProtectionSchemeVo'
import QuotationApplyCalcPlanRequestOpenapi from 'model/remote/price/controller/quotationapply/bill/QuotationApplyCalcPlanRequestOpenapi'

// 报价申请单物流费用对外
export default class QuotationApplyCalcLogisticsFeesRequestOpenapi extends QuotationApplyCalcPlanRequestOpenapi {
  // 装运方案
  packPlanVo: Nullable<PackPlanVo> = null
  // 防护方案
  protectionSchemeVo: Nullable<ProtectionSchemeVo> = null
}