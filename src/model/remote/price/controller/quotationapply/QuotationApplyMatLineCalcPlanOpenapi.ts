import OptionDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/OptionDetailsSpecialConfigVo'
import StaticConfigDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/StaticConfigDetailsSpecialConfigVo'

export default class QuotationApplyMatLineCalcPlanOpenapi {
  // 行号
  line: Nullable<string> = null
  // 物料号。非海运集装箱必填
  matCd: Nullable<string> = null
  // 固定配置明细（带特殊配置）
  staticConfigDetails: StaticConfigDetailsSpecialConfigVo[] = []
  // 选项明细（带特殊配置）
  optionDetails: OptionDetailsSpecialConfigVo[] = []
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: string = ''
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 指导价
  exwPrice: number = 0
  // 台量
  qty: number = 0
  // 自营-起运港代码
  domesticFreightPortCode: Nullable<string> = null
  // 供货-柳工收取装箱费：liugongChargePackagePrice=true时给值
  liugongPackagePrice: Nullable<number> = null
}