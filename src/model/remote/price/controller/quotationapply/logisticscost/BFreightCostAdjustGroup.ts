import BLandBorderFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/BLandBorderFreightCostAdjust'
import BRollScatterFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/BRollScatterFreightCostAdjust'
import DecomposeQuotationFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/DecomposeQuotationFreightCostAdjust'
import DomesticFreightPortCodeCabinetPriceVo from 'model/remote/price/service/quotationapply/freightcalculate/DomesticFreightPortCodeCabinetPriceVo'
import LogisticsInquiryFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/LogisticsInquiryFreightCostAdjust'

export default class BFreightCostAdjustGroup {
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 目的港代码
  destinationPortCode: Nullable<string> = null
  // 目的港名称
  destinationPortName: Nullable<string> = null
  // 集装箱-按柜型柜量
  cabinetPrices: DomesticFreightPortCodeCabinetPriceVo[] = []
  // 滚装散装-按体积
  rollScatterPrices: BRollScatterFreightCostAdjust[] = []
  // 陆运出境-按台量
  qtyPrices: BLandBorderFreightCostAdjust[] = []
  // 物流询价(贸易术语D系列)
  logisticsInquiryList: LogisticsInquiryFreightCostAdjust[] = []
  // 滚装散杂物流询价(滚装散杂)
  decomposeQuotationList: DecomposeQuotationFreightCostAdjust[] = []
}