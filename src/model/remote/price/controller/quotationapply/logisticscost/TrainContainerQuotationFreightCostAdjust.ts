import QuotationApplyMatLineVo from 'model/remote/price/api/quotationapply/calpackplan/QuotationApplyMatLineVo'
import TrainContainerQuotationFreightVo from 'model/remote/price/api/quotationapply/freightcalculate/TrainContainerQuotationFreightVo'

export default class TrainContainerQuotationFreightCostAdjust {
  // 物料信息
  matLineVo: Nullable<QuotationApplyMatLineVo> = null
  // 预算价格
  trainContainerQuotationFreightVo: Nullable<TrainContainerQuotationFreightVo> = null
}