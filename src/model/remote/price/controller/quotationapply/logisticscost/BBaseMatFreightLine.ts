export default class BBaseMatFreightLine {
  // 物料id
  id: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 行号
  line: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 台量
  qty: Nullable<number> = null
  // 发货地/出发地代码
  domesticFreightLandCode: Nullable<string> = null
  // 发货地/出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 滚装/散杂-预算体积
  budgetVolume: Nullable<number> = null
  // 集合站点代码
  collectPositionCode: Nullable<string> = null
  // 集合站点名称
  collectPositionName: Nullable<string> = null
  // 中转运输模式
  collectTransportType: Nullable<string> = null
  // 是否拆解
  decompose: Nullable<boolean> = null
  // 拼箱行号，默认等于行号，物流定制-拼箱时修改
  packLine: Nullable<string> = null
  // 供货-柳工收取装箱费：liugongChargePackagePrice=true时给值
  liugongPackagePrice: Nullable<number> = null
}