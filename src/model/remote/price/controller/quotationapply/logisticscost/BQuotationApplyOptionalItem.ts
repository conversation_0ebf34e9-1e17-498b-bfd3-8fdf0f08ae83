import BQuotationApplyOptionalProtectionPlan from 'model/remote/price/controller/quotationapply/logisticscost/BQuotationApplyOptionalProtectionPlan'

export default class BQuotationApplyOptionalItem {
  // 所属报价申请单, QuotationApplyBill.id
  ownerBill: Nullable<string> = null
  // 自选防护项，JSON字符串
  optionalDetails: Nullable<string> = null
  // 自选防护说明
  optionalDetailsRemark: Nullable<string> = null
  // 自选防护总费用-合同币种
  totalCurr: Nullable<number> = null
  // 自选防护方案物料行明细
  plans: BQuotationApplyOptionalProtectionPlan[] = []
}