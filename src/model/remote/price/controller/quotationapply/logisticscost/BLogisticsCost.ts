import BQuotationApplyBill from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBill'
import PackPlanGroupVo from 'model/remote/price/api/quotationapply/logistics/PackPlanGroupVo'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

export default class BLogisticsCost {
  // 报价申请单
  bill: Nullable<BQuotationApplyBill> = null
  // 组别
  groups: PackPlanGroupVo[] = []
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
}