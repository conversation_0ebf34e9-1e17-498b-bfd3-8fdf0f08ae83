import LogisticsInquiryFreightVo from 'model/remote/price/api/quotationapply/freightcalculate/LogisticsInquiryFreightVo'
import QuotationApplyMatLineVo from 'model/remote/price/api/quotationapply/calpackplan/QuotationApplyMatLineVo'

export default class LogisticsInquiryFreightCostAdjust {
  // 物料信息
  matLineVo: Nullable<QuotationApplyMatLineVo> = null
  // 国际运费
  logisticsInquiryFreightVo: Nullable<LogisticsInquiryFreightVo> = null
}