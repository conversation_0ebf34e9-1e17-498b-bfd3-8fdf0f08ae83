import BQuotationApplyBill from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBill'
import PackPlanVo from 'model/remote/price/api/quotationapply/calpackplan/PackPlanVo'

export default class BPackPlan extends PackPlanVo {
  // 申请单
  bill: Nullable<BQuotationApplyBill> = null
  // 是否必须走物流定制
  isRequiredLogisticsDesign: Nullable<boolean> = null
  // 是否必须走物流询价，针对铁路集装箱特定贸易术语
  requiredTrainContainerQuotationPrice: Nullable<boolean> = null
}