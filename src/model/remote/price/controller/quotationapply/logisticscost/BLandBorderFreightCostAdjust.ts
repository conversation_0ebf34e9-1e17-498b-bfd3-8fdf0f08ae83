import LandBorderFreightVo from 'model/remote/price/api/quotationapply/freightcalculate/LandBorderFreightVo'
import QuotationApplyMatLineVo from 'model/remote/price/api/quotationapply/calpackplan/QuotationApplyMatLineVo'

export default class BLandBorderFreightCostAdjust {
  // 物料信息
  matLineVo: Nullable<QuotationApplyMatLineVo> = null
  // 国际运费
  landBorderFreightVo: Nullable<LandBorderFreightVo> = null
}