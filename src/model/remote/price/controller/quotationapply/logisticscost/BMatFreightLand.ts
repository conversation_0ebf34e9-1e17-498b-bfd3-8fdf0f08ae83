import BBaseMatFreightLine from 'model/remote/price/controller/quotationapply/logisticscost/BBaseMatFreightLine'

export default class BMatFreightLand {
  // 报价申请单id
  id: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 预计发运日期 字符串格式 yyyy-MM-dd
  deliveryDate: Nullable<string> = null
  // 目的港目的站 代码
  destinationPortCode: Nullable<string> = null
  // 目的港目的站 名称
  destinationPortName: Nullable<string> = null
  // 目的港/目的站所属国家代码
  destinationPortCtryCd: Nullable<string> = null
  // 国际运输模式，来自数据字典
  transportTypeId: Nullable<string> = null
  // 国际运输模式类型-集装箱、滚装、散杂，来自数据字典
  transportTypeRoleId: Nullable<string> = null
  // 是否需要柳工工厂内装
  liugongPrePackage: Nullable<boolean> = null
  // 默认起运港代码
  domesticFreightPortCode: Nullable<string> = null
  // 默认起运港名称
  domesticFreightPortName: Nullable<string> = null
  // 供货-交货地点 代码
  domesticFreightLandCode: Nullable<string> = null
  // 供货-交货地点 名称 
  domesticFreightLandName: Nullable<string> = null
  // 供货-柳工是否收取装箱费：liugongPrePackage=true时给值
  liugongChargePackagePrice: Nullable<boolean> = null
  // 供货-国内运输模式
  dmTransportTypeId: Nullable<string> = null
  // 报价物料
  matFreightLines: BBaseMatFreightLine[] = []
}