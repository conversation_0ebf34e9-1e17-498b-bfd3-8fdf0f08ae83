import BFreightCostAdjustGroup from 'model/remote/price/controller/quotationapply/logisticscost/BFreightCostAdjustGroup'
import BQuotationApplyBill from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBill'

export default class BFreightCostAdjust {
  // 报价申请单
  bill: Nullable<BQuotationApplyBill> = null
  // 整单预算报价。不包含工具箱费用
  totalPrice: Nullable<number> = null
  // 整单实际报价。不包含工具箱费用
  totalActualPrice: Nullable<number> = null
  // 组别
  groups: BFreightCostAdjustGroup[] = []
}