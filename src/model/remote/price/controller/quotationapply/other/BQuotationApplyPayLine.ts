// 付款明细
export default class BQuotationApplyPayLine {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  ownerBill: Nullable<string> = null
  // 行号
  line: Nullable<string> = null
  // 付款方式ID，来自数据字典
  paymentTypeId: Nullable<string> = null
  // 付款比例
  paymentRatio: Nullable<number> = null
  // 天数
  days: Nullable<number> = null
  // 远期贴现率
  forwardDiscountRate: Nullable<number> = null
  // 信保费率
  creditInsuranceRate: Nullable<number> = null
  // 信保费率是否申请，true：是 默认为false
  creditInsuranceApply: Nullable<boolean> = null
  // 付款方式是否存在远期
  payForward: Nullable<boolean> = null
  // 申请信保费率
  applyCreditInsuranceRate: Nullable<number> = null
  // 是否需要计算信保费率，true：是 默认为false
  calCreditInsurance: Nullable<boolean> = null
  // 是否使用申请到的费率，如果申请信保费率且审批通过则为true，否则为false
  useCreditInsuranceApply: Nullable<boolean> = null
}