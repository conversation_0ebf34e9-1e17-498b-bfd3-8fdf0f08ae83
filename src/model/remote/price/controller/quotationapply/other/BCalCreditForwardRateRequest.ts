// 远期贴现率和信保费率同时计算请求
export default class BCalCreditForwardRateRequest {
  // 子公司ID
  sbsdyId: Nullable<string> = null
  // 办事处ID
  ofcId: Nullable<string> = null
  // 销往国ID
  ctryId: Nullable<string> = null
  // 客户ID
  custId: Nullable<string> = null
  // 币种，来自数据字典
  currencyId: Nullable<string> = null
  // 预计签署日期
  signDate: Nullable<string> = null
  // 远期天数，格式：yyyy-MM-dd HH:mm:ss
  days: Nullable<number> = null
  // 付款方式ID，来自数据字典
  paymentTypeId: Nullable<string> = null
  // 出口类型，来自数据字典
  exportTypeId: Nullable<string> = null
}