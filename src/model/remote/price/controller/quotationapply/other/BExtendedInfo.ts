// 延保信息
export default class BExtendedInfo {
  // 延保政策id
  policyId: Nullable<string> = null
  // 延保政策名称中文
  policyNameCn: Nullable<string> = null
  // 延保政策名称英文
  policyNameEn: Nullable<string> = null
  // 延保政策价格
  price: Nullable<number> = null
  // 币种
  currency: Nullable<string> = null
  // 赔付类型 F: 表示整机， K： 表示关键部件
  type: Nullable<string> = null
  // 政策执行开始时间， 格式：yyyy-mm-dd
  beginDate: Nullable<Date> = null
  // 政策执行结束时间，格式：yyyy-mm-dd
  endDate: Nullable<Date> = null
  // 保修截止读数
  endHourReading: Nullable<number> = null
  // 发运日期到保修截止日期相隔多少个月
  fromShippingDate: Nullable<number> = null
  // 交机日期到保修截至日期相隔多少个月
  fromDeliveryDate: Nullable<number> = null
  // 日期间隔时长单位
  dateUnit: Nullable<string> = null
}