import { InterTransPremiumRuleType } from 'model/remote/price/api/quotationapply/bill/InterTransPremiumRuleType'

// 国际运输保险费规则
export default class BInterTransPremiumRule {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  owner: Nullable<string> = null
  // 加价系数
  advanceRate: Nullable<number> = null
  // 费率
  rate: Nullable<number> = null
  // 类型-舱内、舱外
  type: Nullable<InterTransPremiumRuleType> = null
  // 是否申请加投额外险
  addInsurance: Nullable<boolean> = null
}