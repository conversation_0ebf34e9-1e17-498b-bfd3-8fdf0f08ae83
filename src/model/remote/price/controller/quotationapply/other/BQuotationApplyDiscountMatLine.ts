import BExtendedInfo from 'model/remote/price/controller/quotationapply/other/BExtendedInfo'

// 其他费用-报价物料明细
export default class BQuotationApplyDiscountMatLine {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  owner: Nullable<string> = null
  // 行号
  line: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 台量
  qty: Nullable<number> = null
  // 延保政策
  extendedInfos: BExtendedInfo[] = []
  // 延保费/台
  extendedFee: Nullable<number> = null
  // 客户佣金/台
  commission: Nullable<number> = null
  // 赠送配件金额/台
  accessoryFee: Nullable<number> = null
  // 额外费用/台
  extraFee: Nullable<number> = null
  // 额外费用用途。非必填。当额外费用不为空时必填
  extraFeeUsage: Nullable<string> = null
}