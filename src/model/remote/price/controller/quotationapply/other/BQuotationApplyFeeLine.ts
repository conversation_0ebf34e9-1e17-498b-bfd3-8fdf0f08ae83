import { QuotationApplyFeeType } from 'model/remote/price/api/quotationapply/fee/QuotationApplyFeeType'

// 报价申请费用明细
export default class BQuotationApplyFeeLine {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  ownerBill: Nullable<string> = null
  // 所属物料明细，QuotationApplyMatLine.id
  ownerLine: Nullable<string> = null
  // 物料行号
  matLine: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 台量
  qty: Nullable<number> = null
  // 额外费用用途
  extraFeeUsage: Nullable<string> = null
  // 费用类型 物流费用、其他费用
  type: Nullable<QuotationApplyFeeType> = null
  // 费用code
  feeCode: Nullable<string> = null
  // 费用金额
  occur: Nullable<number> = null
  // 费用总金额
  occurTotal: Nullable<number> = null
}