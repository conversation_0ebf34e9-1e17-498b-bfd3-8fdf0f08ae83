import BInterTransPremiumRule from 'model/remote/price/controller/quotationapply/other/BInterTransPremiumRule'
import BQuotationApplyFee from 'model/remote/price/controller/quotationapply/other/BQuotationApplyFee'
import BQuotationApplyFeeLine from 'model/remote/price/controller/quotationapply/other/BQuotationApplyFeeLine'

// 其他费用-费用汇总对象
export default class BQuotationApplyOtherFee {
  // ID,存储单号
  id: Nullable<string> = null
  // 版本号
  version: number = 0
  // 其他费用总金额
  totalAmount: Nullable<number> = null
  // 报价申请费用
  applyFees: BQuotationApplyFee[] = []
  // 报价申请费用明细
  applyFeeLines: BQuotationApplyFeeLine[] = []
  // 国际运输保险费规则
  rule: Nullable<BInterTransPremiumRule> = null
}