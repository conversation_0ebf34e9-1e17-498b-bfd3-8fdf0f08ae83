import { QuotationApplyFeeType } from 'model/remote/price/api/quotationapply/fee/QuotationApplyFeeType'

// 报价申请费用
export default class BQuotationApplyFee {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  ownerBill: Nullable<string> = null
  // 费用类型 物流费用、其他费用
  type: Nullable<QuotationApplyFeeType> = null
  // 费用code
  feeCode: Nullable<string> = null
  // 费用金额
  occur: Nullable<number> = null
}