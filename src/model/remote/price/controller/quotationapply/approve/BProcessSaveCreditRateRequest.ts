import BPayLineCreditRate from 'model/remote/price/controller/quotationapply/approve/BPayLineCreditRate'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 报价申请单保存信保费率请求
export default class BProcessSaveCreditRateRequest {
  // ID,存储单号
  id: Nullable<string> = null
  // 版本号
  version: number = 0
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
  // 付款方式费率明细
  payLineCreditRates: BPayLineCreditRate[] = []
  // 留言
  comment: Nullable<string> = null
}