import BQuotationApplyProcessGetOptionalItem from 'model/remote/price/controller/quotationapply/approve/BQuotationApplyProcessGetOptionalItem'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

export default class BQuotationApplyProcessSaveOptionalItem extends BQuotationApplyProcessGetOptionalItem {
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
}