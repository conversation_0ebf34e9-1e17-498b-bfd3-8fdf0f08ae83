import BQuotationApplyProcessOptionalItemPlan from 'model/remote/price/controller/quotationapply/approve/BQuotationApplyProcessOptionalItemPlan'

export default class BQuotationApplyProcessGetOptionalItem {
  // ID,存储单号
  id: string = ''
  // 版本号
  version: number = 0
  // 自选防护项，JSON字符串
  optionalDetails: Nullable<string> = null
  // 自选防护说明
  optionalDetailsRemark: Nullable<string> = null
  // 自选防护方案物料行明细
  plans: BQuotationApplyProcessOptionalItemPlan[] = []
}