import MatLineCostPrice from 'model/remote/price/controller/quotationapply/approve/MatLineCostPrice'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 报价申请单填写成本价请求
export default class BSaveCostPriceRequest {
  // ID,存储单号
  id: string = ''
  // 版本号
  version: number = 0
  // 物料成本价明细
  matLineCostPrices: MatLineCostPrice[] = []
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
  // 留言
  comment: Nullable<string> = null
}