import BPayLineCreditRate from 'model/remote/price/controller/quotationapply/approve/BPayLineCreditRate'
import CreditInsuranceRateDetailVo from 'model/remote/price/model/vo/feerate/creditinsurancerate/CreditInsuranceRateDetailVo'

export default class BCreditInsuranceRateAndCountry {
  // 注册国家id
  ctryId: Nullable<number> = null
  // 注册国家code
  ctryCd: Nullable<string> = null
  // 注册国家名称
  ctryNm: Nullable<string> = null
  // 申请说明
  applyReason: Nullable<string> = null
  // 付款方式费率明细
  payLineCreditRates: BPayLineCreditRate[] = []
  // 参考信保费率
  rates: CreditInsuranceRateDetailVo[] = []
  // 合同总金额
  totalAmount: Nullable<number> = null
}