import BExtendedInfo from 'model/remote/price/controller/quotationapply/other/BExtendedInfo'
import OptionDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/OptionDetailsSpecialConfigVo'
import StaticConfigDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/StaticConfigDetailsSpecialConfigVo'

export default class QuotationApplyMatLineCreateOpenapi {
  // 行号
  line: string = ''
  // 物料号
  matCd: string = ''
  // 固定配置明细（带特殊配置）
  staticConfigDetails: StaticConfigDetailsSpecialConfigVo[] = []
  // 选项明细（带特殊配置）
  optionDetails: OptionDetailsSpecialConfigVo[] = []
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: string = ''
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 年度指导价（反写时使用）
  yearGuidePrice: number = 0
  // 实际报价(对应实际报价)
  exwPrice: number = 0
  // 台量
  qty: number = 0
  // 额外费用/台
  extraFee: Nullable<number> = null
  // 额外费用用途。非必填。当额外费用不为空时必填
  extraFeeUsage: Nullable<string> = null
  // 自营-起运港代码
  domesticFreightPortCode: Nullable<string> = null
  // 自营-客户佣金/台
  commission: Nullable<number> = null
  // 自营-赠送配件金额/台
  accessoryFee: Nullable<number> = null
  // 供货-柳工收取装箱费：liugongChargePackagePrice=true时给值
  liugongPackagePrice: Nullable<number> = null
  // 延保信息
  extendedInfo: BExtendedInfo[] = []
}