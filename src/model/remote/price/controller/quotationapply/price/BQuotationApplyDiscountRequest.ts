import BQuotationApplyPriceDiscountMatLine from 'model/remote/price/controller/quotationapply/price/BQuotationApplyPriceDiscountMatLine'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 保存折扣率对象
export default class BQuotationApplyDiscountRequest {
  // ID,存储单号
  id: Nullable<string> = null
  // 版本号
  version: number = 0
  // 物料明细
  matLines: BQuotationApplyPriceDiscountMatLine[] = []
  // 流程出口. 非新建场景，get接口有且仅会返回一个出口
  outGoing: Nullable<UserTaskOutGoing> = null
}