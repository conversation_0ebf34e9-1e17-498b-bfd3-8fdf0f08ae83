import BProdGrpDiscountLine from 'model/remote/price/controller/quotationapply/price/BProdGrpDiscountLine'
import BQuotationApplyBill from 'model/remote/price/controller/quotationapply/bill/BQuotationApplyBill'
import BQuotationDiscountApply from 'model/remote/price/controller/quotationapply/price/BQuotationDiscountApply'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 报价总览-计算折扣对象
export default class BQuotationApplyDiscount {
  // ID,存储单号
  id: Nullable<string> = null
  // 折扣率
  discountRate: Nullable<number> = null
  // 报价总览-计算折扣-折扣申请说明对象
  quotationDiscountApply: Nullable<BQuotationDiscountApply> = null
  // 报价总览-计算折扣-产品线明细
  prodGrpLines: BProdGrpDiscountLine[] = []
  // 流程出口. 非新建场景，get接口有且仅会返回一个出口
  outGoing: Nullable<UserTaskOutGoing> = null
  // 报价申请单
  bill: Nullable<BQuotationApplyBill> = null
}