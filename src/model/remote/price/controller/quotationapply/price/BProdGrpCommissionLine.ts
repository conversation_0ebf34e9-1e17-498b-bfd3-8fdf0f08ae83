import BProdMatCommissionLine from 'model/remote/price/controller/quotationapply/price/BProdMatCommissionLine'

// 报价总览-子公司佣金-产品线明细
export default class BProdGrpCommissionLine {
  // 行号
  line: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 子公司佣金率
  sbsdyCommissionRate: Nullable<number> = null
  // 子公司佣金小计
  sbsdyCommissionAmount: Nullable<number> = null
  // 报价总览-子公司佣金-产品线物料明细
  prodMatLines: BProdMatCommissionLine[] = []
}