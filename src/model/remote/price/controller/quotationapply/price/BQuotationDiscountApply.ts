import BMatCompetitiveInfo from 'model/remote/price/controller/quotationapply/price/BMatCompetitiveInfo'
import BQuotationDiscountApplyAttachment from 'model/remote/price/controller/quotationapply/price/BQuotationDiscountApplyAttachment'

// 报价总览-计算折扣-折扣申请说明对象
export default class BQuotationDiscountApply {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  ownerBill: Nullable<string> = null
  // 产品信息
  productInfoList: string[] = []
  // 市场信息
  marketInfoList: string[] = []
  // 客户ID
  cusId: Nullable<string> = null
  // 客户名称
  cusName: Nullable<string> = null
  // 客户行业,来自数据字典
  cusField: Nullable<string> = null
  // 客户需求
  cusNeed: Nullable<string> = null
  // 说明，html字符串存储
  remark: Nullable<string> = null
  // 物料竞品信息
  matCompetitiveInfos: BMatCompetitiveInfo[] = []
  // 物料竞品信息附件
  attachments: BQuotationDiscountApplyAttachment[] = []
}