import BProdMatDiscountLine from 'model/remote/price/controller/quotationapply/price/BProdMatDiscountLine'

// 报价总览-计算折扣-产品线明细
export default class BProdGrpDiscountLine {
  // 行号
  line: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 折扣率
  discountRate: Nullable<number> = null
  // 折扣金额
  discountAmount: Nullable<number> = null
  // 折扣金额-合同币种
  discountAmountCurrency: Nullable<number> = null
  // 可用额度
  availableLimit: Nullable<number> = null
  // 报价总览-计算折扣-产品线物料明细
  prodMatLines: BProdMatDiscountLine[] = []
}