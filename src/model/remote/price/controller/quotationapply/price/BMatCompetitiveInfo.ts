import BMatCompetitiveInfoLine from 'model/remote/price/controller/quotationapply/price/BMatCompetitiveInfoLine'

// 物料竞品信息
export default class BMatCompetitiveInfo {
  // 行号
  line: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 折扣率
  discountRate: Nullable<number> = null
  // 明细
  lines: BMatCompetitiveInfoLine[] = []
}