import BCompetitiveInfo from 'model/remote/price/controller/quotationapply/price/BCompetitiveInfo'

export default class BMatCompetitiveInfoLine {
  // 行号
  line: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 竞品信息
  competitiveInfos: BCompetitiveInfo[] = []
}