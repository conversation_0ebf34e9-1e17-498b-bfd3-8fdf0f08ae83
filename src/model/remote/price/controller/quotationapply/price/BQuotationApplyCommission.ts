import BProdGrpCommissionLine from 'model/remote/price/controller/quotationapply/price/BProdGrpCommissionLine'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 报价总览-子公司佣金对象
export default class BQuotationApplyCommission {
  // ID,存储单号
  id: Nullable<string> = null
  // 版本号
  version: number = 0
  // 提佣子公司ID
  commissionSbsdyId: Nullable<string> = null
  // 整单子公司佣金合计
  commissionSbsdyAmount: Nullable<number> = null
  // 报价总览-子公司佣金-产品线明细
  prodGrpLines: BProdGrpCommissionLine[] = []
  // 流程出口. 非新建场景，get接口有且仅会返回一个出口
  outGoing: Nullable<UserTaskOutGoing> = null
}