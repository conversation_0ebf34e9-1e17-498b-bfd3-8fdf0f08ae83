import FeeOpenapi from 'model/remote/price/controller/quotationapply/logisticscost/FeeOpenapi'
import OptionDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/OptionDetailsSpecialConfigVo'
import StaticConfigDetailsSpecialConfigVo from 'model/remote/price/api/quotationapply/bill/StaticConfigDetailsSpecialConfigVo'

export default class QuotationApplyPriceMatLineOpenapi {
  // ID
  id: Nullable<string> = null
  // 所属报价申请单, QuotationApplyBill.id
  owner: Nullable<string> = null
  // 行号
  line: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 固定配置明细（带特殊配置）
  staticConfigDetails: StaticConfigDetailsSpecialConfigVo[] = []
  // 选项明细（带特殊配置）
  optionDetails: OptionDetailsSpecialConfigVo[] = []
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 指导价
  exwPrice: Nullable<number> = null
  // 是否使用特价
  useSpecial: Nullable<boolean> = null
  // 台量
  qty: Nullable<number> = null
  // 发货地/出发地代码
  domesticFreightLandCode: Nullable<string> = null
  // 发货地/出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortName: Nullable<string> = null
  // 折扣率
  discountRate: Nullable<number> = null
  // 设备报价/台
  actualPrice: Nullable<number> = null
  // 综合报价/台
  quotationTotal: Nullable<number> = null
  // 国内段运杂费
  domesticFreight: Nullable<number> = null
  // 国际/境外运费
  internationalFreight: Nullable<number> = null
  // 其他费用
  otherFreight: Nullable<number> = null
  // 设备总报价（物料行）
  actualPriceLineTotal: Nullable<number> = null
  // 综合总报价（物料行）
  quotationTotalLineTotal: Nullable<number> = null
  // 国内段总运杂费（物料行）
  domesticFreightLineTotal: Nullable<number> = null
  // 国际/境外总运费（物料行）
  internationalFreightLineTotal: Nullable<number> = null
  // 其他总费用（物料行）
  otherFreightLineTotal: Nullable<number> = null
  // 物流费用明细
  logisticsFees: FeeOpenapi[] = []
  // 其他费用明细
  otherFees: FeeOpenapi[] = []
}