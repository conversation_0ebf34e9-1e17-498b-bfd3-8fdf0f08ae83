import DecomposeQuotationFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/DecomposeQuotationFreightCostAdjust'
import LogisticsInquiryFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/LogisticsInquiryFreightCostAdjust'
import TrainContainerQuotationFreightCostAdjust from 'model/remote/price/controller/quotationapply/logisticscost/TrainContainerQuotationFreightCostAdjust'

export default class BLogisticsInquiryGroup {
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 目的港代码
  destinationPortCode: Nullable<string> = null
  // 目的港名称
  destinationPortName: Nullable<string> = null
  // 物流询价(贸易术语D系列)
  logisticsInquiryList: LogisticsInquiryFreightCostAdjust[] = []
  // 物流询价(滚装散杂)
  decomposeQuotationList: DecomposeQuotationFreightCostAdjust[] = []
  // 物流询价(铁路集装箱)
  trainContainerQuotationList: TrainContainerQuotationFreightCostAdjust[] = []
}