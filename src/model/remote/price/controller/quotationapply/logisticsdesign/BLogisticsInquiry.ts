import BLogisticsInquiryGroup from 'model/remote/price/controller/quotationapply/logisticsdesign/BLogisticsInquiryGroup'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

export default class BLogisticsInquiry {
  // 报价申请单id
  id: Nullable<string> = null
  // 版本号
  version: Nullable<number> = null
  // 分组
  groups: BLogisticsInquiryGroup[] = []
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
  // 滚装散杂-物流询价,是否需要填入预算报价
  requiredDecomposeQuotationPrice: Nullable<boolean> = null
}