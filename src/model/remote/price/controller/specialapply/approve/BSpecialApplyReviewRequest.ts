import SpecialApplyPriceOverviewMatLineV2 from 'model/remote/price/api/specialapply/bill/SpecialApplyPriceOverviewMatLineV2'
import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 特价申请单归档请求
export default class BSpecialApplyReviewRequest {
  // ID,存储单号
  id: string = ''
  // 版本号
  version: number = 0
  // 物料归档明细
  matLines: SpecialApplyPriceOverviewMatLineV2[] = []
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
  // 留言
  comment: Nullable<string> = null
}