import UserTaskOutGoing from 'model/remote/price/api/quotationapply/bill/bpfm/UserTaskOutGoing'

// 特价申请单保存是否允许折扣请求
export default class BSaveSpecialApplyBillDiscountRequest {
  // ID,存储单号
  id: string = ''
  // 版本号
  version: number = 0
  // 允许特价产生折扣
  discount: Nullable<boolean> = null
  // 任务出口，回传
  userTaskOutGoing: UserTaskOutGoing = new UserTaskOutGoing()
  // 留言
  comment: Nullable<string> = null
}