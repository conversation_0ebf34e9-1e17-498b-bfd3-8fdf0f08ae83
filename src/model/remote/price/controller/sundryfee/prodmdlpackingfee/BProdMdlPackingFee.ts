import BProdMdlPackingFeeDetail from 'model/remote/price/controller/sundryfee/prodmdlpackingfee/BProdMdlPackingFeeDetail'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { ProdMdlPackingFeeState } from 'model/remote/price/api/sundryfee/prodmdlpackingfee/ProdMdlPackingFeeState'

export default class BProdMdlPackingFee extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 状态
  state: Nullable<ProdMdlPackingFeeState> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 版本号
  version: Nullable<number> = null
  // 装箱费明细
  detailList: BProdMdlPackingFeeDetail[] = []
}