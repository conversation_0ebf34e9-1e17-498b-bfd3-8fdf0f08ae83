import BProdMdlPackingFeeDetail from 'model/remote/price/controller/sundryfee/prodmdlpackingfee/BProdMdlPackingFeeDetail'
import ProdMdlLines from 'model/remote/price/controller/sundryfee/prodmdlpackingfee/ProdMdlLines'

export default class BProdMdlPackingFeeCreateRequest {
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 装箱费明细
  detailList: BProdMdlPackingFeeDetail[] = []
  // 机型明细
  prodMdlLines: ProdMdlLines[] = []
}