import PageQueryRequest from 'model/remote/support/common/PageQueryRequest'

// 包装防护费查询过滤器
export default class BPackagingProtectionPriceFilter extends PageQueryRequest {
  // 运输模式等于
  modeEquals: Nullable<string> = null
  // 机型代码类似于
  prodModelCdLike: Nullable<string> = null
  // 国际产品线分类id等于
  i18ProdGroupSortIdEquals: Nullable<string> = null
  // 有效期开始日期起始于
  effectiveStartDateStart: Nullable<Date> = null
  // 有效期开始日期截至于
  effectiveStartDateEnd: Nullable<Date> = null
  // 有效期结束日期起始于
  effectiveEndDateStart: Nullable<Date> = null
  // 有效期结束日期截至于
  effectiveEndDateEnd: Nullable<Date> = null
  // 创建时间起始于
  createTimeStart: Nullable<Date> = null
  // 创建时间截止于
  createTimeEnd: Nullable<Date> = null
  // 状态 等于
  stateEq: Nullable<string> = null
  // 单号 类似于
  idLike: Nullable<string> = null
}