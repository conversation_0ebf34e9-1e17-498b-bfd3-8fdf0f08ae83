import BPackagingProtectionModel from 'model/remote/price/controller/sundryfee/packagingprotectionprice/BPackagingProtectionModel'
import LtcEntityVo from 'model/remote/support/core/domain/LtcEntityVo'
import { PackagingProtectionPriceState } from 'model/remote/price/api/sundryfee/packagingprotectionprice/PackagingProtectionPriceState'
import { TransportType } from 'model/remote/price/model/po/TransportType'

export default class BPackagingProtectionPrice extends LtcEntityVo {
  // id
  id: Nullable<string> = null
  // 运输模式
  mode: Nullable<TransportType> = null
  // 国际产品线分类id
  i18ProdGroupSortId: Nullable<string> = null
  // 国际产品线分类名称
  i18ProdGroupSortName: Nullable<string> = null
  // 全部机型
  allProdModel: Nullable<boolean> = null
  // 有效期开始日期
  effectiveStartDate: Nullable<Date> = null
  // 有效期结束日期
  effectiveEndDate: Nullable<Date> = null
  // 状态
  state: Nullable<PackagingProtectionPriceState> = null
  // 必备防护价格
  price: Nullable<number> = null
  // 版本号
  version: Nullable<number> = null
  // 机型明细
  modelList: BPackagingProtectionModel[] = []
}