import BpfmQueryRequest from 'model/remote/support/feign/bpfm/request/BpfmQueryRequest'

export default class BpfmQueryResourceModuleRequest extends BpfmQueryRequest {
  // 应用系统代码
  appCode: Nullable<string> = null
  // 用途[bpm/guide]，为兼容历史功能，默认值=bpm
  usage: Nullable<string> = null
  // 关键字
  keyword: Nullable<string> = null
  // id等于
  idEq: Nullable<string> = null
  // code在...之中
  codeIn: string[] = []
}