import BpfmResourceAction from 'model/remote/price/api/bpfm/resourcemodule/BpfmResourceAction'
import BpfmResourceActionPanel from 'model/remote/price/api/bpfm/resourcemodule/BpfmResourceActionPanel'
import BpfmResourceBizState from 'model/remote/price/api/bpfm/resourcemodule/BpfmResourceBizState'
import BpfmResourcePage from 'model/remote/price/api/bpfm/resourcemodule/BpfmResourcePage'
import BpfmResourceSubtype from 'model/remote/price/api/bpfm/resourcemodule/BpfmResourceSubtype'
import BpfmResourceVariable from 'model/remote/price/api/bpfm/resourcemodule/BpfmResourceVariable'

export default class BpfmResourceModuleDto {
  // uuid
  uuid: Nullable<string> = null
  // 模块代码
  code: Nullable<string> = null
  // 模块名称
  name: Nullable<string> = null
  // 服务id
  serviceId: Nullable<string> = null
  // 服务路径
  servicePath: Nullable<string> = null
  // 模块描述
  description: Nullable<string> = null
  // 资源页面列表
  pages: BpfmResourcePage[] = []
  // 资源动作列表
  actions: BpfmResourceAction[] = []
  // 资源操作面列表
  actionPanels: BpfmResourceActionPanel[] = []
  // 资源状态列表
  bizStates: BpfmResourceBizState[] = []
  // 资源子类别列表
  subtypes: BpfmResourceSubtype[] = []
  // 资源变量列表
  variables: BpfmResourceVariable[] = []
  // 资源属性列表
  attributes: BpfmResourceVariable[] = []
}