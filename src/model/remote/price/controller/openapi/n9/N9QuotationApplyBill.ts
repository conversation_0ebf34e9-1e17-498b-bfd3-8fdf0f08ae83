import N9Fee from 'model/remote/price/controller/openapi/n9/N9Fee'
import N9QuotationApplyMat from 'model/remote/price/controller/openapi/n9/N9QuotationApplyMat'
import N9QuotationApplyPayLine from 'model/remote/price/controller/openapi/n9/N9QuotationApplyPayLine'
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType'

// n9报价申请单
export default class N9QuotationApplyBill {
  // 单号，多值以逗号分隔拼接
  id: Nullable<string> = null
  // 主题，多值以逗号分隔拼接
  topic: Nullable<string> = null
  // 申请类型，多值以逗号分隔拼接
  contractType: Nullable<string> = null
  // 客户ID
  custId: Nullable<string> = null
  // 客户代码
  custCode: Nullable<string> = null
  // 客户名称
  custName: Nullable<string> = null
  // 办事处ID
  ofcId: Nullable<string> = null
  // SAP办事处CD
  sapOfcCd: Nullable<string> = null
  // 办事处名称
  ofcName: Nullable<string> = null
  // 办事处名称(英文)
  ofcNameEn: Nullable<string> = null
  // 子公司ID
  sbsdyId: Nullable<string> = null
  // SAP子公司CD
  sapSbsdyCd: Nullable<string> = null
  // 子公司名称
  sbsdyName: Nullable<string> = null
  // 子公司名称(英文)
  sbsdyNameEn: Nullable<string> = null
  // 销往国ID
  ctryId: Nullable<string> = null
  // 国家编号2位
  ctryCd: Nullable<string> = null
  // 销往国名称
  ctryName: Nullable<string> = null
  // 销售模式
  saleMode: Nullable<GuidePriceType> = null
  // 贸易术语，来自数据字典
  incotermsId: Nullable<string> = null
  // 币种id，来自数据字典
  currencyId: Nullable<string> = null
  // 币种名称，来自数据字典
  currencyName: Nullable<string> = null
  // 币种符号，来自数据字典
  currencySymbol: Nullable<string> = null
  // 合同币种折CNY汇率
  cnyExRate: Nullable<number> = null
  // 合同币种折USD汇率
  usdExRate: Nullable<number> = null
  // 预计签署日期
  signDate: Nullable<string> = null
  // 物料台量合计
  qtyTotal: Nullable<number> = null
  // 出口类型，来自数据字典:self:自营，supply:供货
  exportTypeId: Nullable<string> = null
  // 预计发货日期
  deliveryDate: Nullable<string> = null
  // 提佣子公司ID
  commissionSbsdyId: Nullable<string> = null
  // 付款方式信息
  payLines: N9QuotationApplyPayLine[] = []
  // 物料信息
  mats: N9QuotationApplyMat[] = []
  // 费用项合计
  fees: N9Fee[] = []
}