import N9ExtendedInfo from 'model/remote/price/controller/openapi/n9/N9ExtendedInfo'
import N9MatFee from 'model/remote/price/controller/openapi/n9/N9MatFee'
import N9MatSbsdyCommission from 'model/remote/price/controller/openapi/n9/N9MatSbsdyCommission'

// 物料信息
export default class N9QuotationApplyMat {
  // 物料ID
  id: Nullable<string> = null
  // 所属报价申请单
  owner: Nullable<string> = null
  // 行号
  line: Nullable<string> = null
  // 物料号
  matCd: Nullable<string> = null
  // 国际产品线id
  i18ProdGroupId: Nullable<string> = null
  // 国际产品线名称
  i18ProdGroupName: Nullable<string> = null
  // 研发产品组id
  prodGrpId: Nullable<string> = null
  // 研发产品组名称
  prodGrpName: Nullable<string> = null
  // 机型ID
  prodMdlId: Nullable<string> = null
  // 机型代码
  prodMdlCd: Nullable<string> = null
  // 物料描述
  matDesc: Nullable<string> = null
  // 物料描述(英文)
  matDescEn: Nullable<string> = null
  // 指导价
  exwPrice: Nullable<number> = null
  // 台量
  qty: Nullable<number> = null
  // 设备报价/台
  actualPrice: Nullable<number> = null
  // 设备总报价（物料行）
  actualPriceLineTotal: Nullable<number> = null
  // 综合报价/台
  quotationTotal: Nullable<number> = null
  // 综合总报价（物料行）
  quotationTotalLineTotal: Nullable<number> = null
  // 预计发运日期 字符串格式 yyyy-MM-dd
  deliveryDate: Nullable<string> = null
  // 发货地/出发地代码
  domesticFreightLandCode: Nullable<string> = null
  // 发货地/出发地名称
  domesticFreightLandName: Nullable<string> = null
  // 目的港代码
  destinationPortCode: Nullable<string> = null
  // 目的港名称
  destinationPortName: Nullable<string> = null
  // 国际运输模式，来自数据字典
  transportTypeId: Nullable<string> = null
  // 国际运输模式类型-集装箱、滚装、散杂，来自数据字典
  transportTypeRoleId: Nullable<string> = null
  // 起运港/起运站代码
  domesticFreightPortCode: Nullable<string> = null
  // 起运港/起运站名称
  domesticFreightPortName: Nullable<string> = null
  // 供货-交货地点 代码
  dmDomesticFreightLandCode: Nullable<string> = null
  // 供货-交货地点 名称
  dmDomesticFreightLandName: Nullable<string> = null
  // 供货-国内运输模式
  dmTransportTypeId: Nullable<string> = null
  // 延保政策
  extendedInfos: N9ExtendedInfo[] = []
  // 物料信息：装运方案
  shipPlan: Nullable<string> = null
  // 物料各项费用明细
  fees: N9MatFee[] = []
  // 子公司佣金
  matSbsdyCommission: Nullable<N9MatSbsdyCommission> = null
  // 额外费用用途。非必填。当额外费用不为空时必填
  extraFeeUsage: Nullable<string> = null
}