export default class BYearGuidePriceMatchResult {
  // 物料号
  materialCode: Nullable<string> = null
  // 未匹配到指导价
  noMatchedPrice: Nullable<boolean> = null
  // 指导价
  exwPrice: Nullable<number> = null
  // 指导价币种
  exwPriceCurrencyCode: Nullable<string> = null
  // 指导价id
  exwPriceId: Nullable<string> = null
  // 是否有特价
  hasSpecialPrice: Nullable<boolean> = null
  // 特价
  specialPrice: Nullable<number> = null
  // 特价币种
  specialPriceCurrencyCode: Nullable<string> = null
  // 特价指导价id
  specialPriceId: Nullable<string> = null
  // 特价是否限制数量
  limitMat: Nullable<boolean> = null
  // 特价限制数量
  limitQty: Nullable<number> = null
  // 特价已使用数量
  useQty: Nullable<number> = null
  // 特价剩余限量
  specialPriceMaxQty: Nullable<number> = null
  // 特价是否允许继续产生折扣
  discount: Nullable<boolean> = null
}