import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType'

export default class BYearGuidePriceBatchMatchRequest {
  // 物料号 in
  materialCodeIn: string[] = []
  // 子公司ID等于
  subsidiaryIdEquals: Nullable<string> = null
  // 办事处ID等于
  officeIdEquals: Nullable<string> = null
  // 国家ID等于
  countryIdIdEquals: Nullable<string> = null
  // 币种等于
  currencyCodeEquals: Nullable<string> = null
  // 客户ID等于
  customIdEquals: Nullable<string> = null
  // 预计签署日期 字符串 yyyy-MM-dd
  signedDate: Nullable<string> = null
  // 出口类型id 等于
  exportTypeId: Nullable<string> = null
  // 销售模式。当出口类型为自营时有值且必填
  saleMode: Nullable<GuidePriceType> = null
}