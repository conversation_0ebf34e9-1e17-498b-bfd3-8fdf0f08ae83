export default class DomesticFreightPortCodeCabinetPriceVo {
  // 主键（uuid）
  id: Nullable<string> = null
  // 起运港/起运站
  domesticFreightPortCode: Nullable<string> = null
  // 柜型
  cabinetType: Nullable<string> = null
  // 柜量
  cabinetQty: Nullable<number> = null
  // 原币单价
  originPrice: Nullable<number> = null
  // 原币币种
  originCurrency: Nullable<string> = null
  // 预报单价原币兑合并币种汇率
  exchangeRate: Nullable<number> = null
  // 预报单价（合同币种）
  price: Nullable<number> = null
  // 预报合计（合同币种）
  totalPrice: Nullable<number> = null
  // 实际报价（合同币种）
  actualPrice: Nullable<number> = null
  // 实报合计（合同币种）
  totalActualPrice: Nullable<number> = null
  // 运费规则id
  ruleId: Nullable<string> = null
}