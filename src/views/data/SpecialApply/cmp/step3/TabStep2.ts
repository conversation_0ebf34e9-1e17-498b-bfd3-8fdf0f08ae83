import { Vue, Component, Prop } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import { RouterNames } from '@/router/modules';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import CommonInfoApi from '@/http/data/CommonInfoApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import ApplyDescription from '@/model/remote/price/api/specialapply/bill/ApplyDescription';
import SpecialApplySupplementPrice from '@/model/remote/price/api/specialapply/priceoverview/SpecialApplySupplementPrice';
// COMPONENTES
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import Dialog from '@/components/dialog/Dialog';
import SpecialSubmitDialog from '../drawer/SpecialSubmitDialog.vue';
import FormItem from '@/components/form/form-item/index.vue';
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';

@Component({
  name: 'TabStep2',
  components: { NumberInput, FormItem, SearchRemote },
})
export default class TabStep2 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  $refs: any;
  PrecisionUtil = PrecisionUtil;
  ObjectUtil = ObjectUtil;
  entity: SpecialApplySupplementPrice = new SpecialApplySupplementPrice();
  showFillDrawer: boolean = false; // 特价申请说明抽屉是否打开
  specialApplyDescription: ApplyDescription = new ApplyDescription(); // 特价申请说明

  // 判断是否为FOB场景
  get isFOB() {
    return this.baseEntity.incotermsId === 'FOB';
  }
  rules: any = {
    // 币种验证动态验证
    supplementPriceCcyId: [
      { required: true, message: '请选择', trigger: ['blur', 'change'] },
      {
        required: false,
        validator: (rule: any, value: any, callback: Function) =>
          this.validateCurrency(rule, value, callback),
        trigger: ['blur', 'change'],
      },
    ],
    // 终端价格和经销商采购成本必填
    // terminalPrice: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
    // dlrProcurementCost: [{ required: true, message: '请输入', trigger: ['blur', 'change'] }],
  };

  created() {
    this.getData();
  }
  getData() {
    // 获取补充价格信息
    SpecialApplyBillApi.getSupplementPrice(this.baseEntity.id!)
      .then((res) => {
        this.entity = res.data!;
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }
  // 币种查询
  queryCurrencyList(query, page) {
    let par = {
      codeOrNameLike: query,
      dictTypeEquals: 'ccy',
      lang: this.$i18n.locale,
      pageNum: page,
      pageSize: 20,
    };
    return CommonInfoApi.currencyList(par);
  }
  // 币种回显方法
  queryCurrencyInit(value) {
    let paramIn = {
      codeOrNameLike: value,
      dictTypeEquals: 'ccy',
      lang: this.$i18n.locale,
      pageNum: 1,
      pageSize: 1,
    };
    return CommonInfoApi.currencyList(paramIn);
  }
  currencyValueFormat(item) {
    return item.dictValue;
  }
  currencyLabelFormat(item) {
    return '[' + item.dictValue + ']' + item.dictLabel;
  }
  // 币种变动
  currencyIdChange(value) {
    if (value) {
      this.entity.supplementPriceCcyName = value.dictLabel;
      this.entity.supplementPriceCcySymbol = value.ext;
    } else {
      this.entity.supplementPriceCcyName = null;
      this.entity.supplementPriceCcySymbol = null;
    }
  }
  // 动态验证币种：若已填写任一费用项且非0，则【币种】必选
  validateCurrency(rule: any, value: any, callback: Function) {
    const hasAnyFee =
      this.entity.lines &&
      this.entity.lines.some((line: any) => {
        return (
          (line.terminalPrice && line.terminalPrice !== 0) ||
          (line.dlrProcurementCost && line.dlrProcurementCost !== 0) ||
          (line.inlandFreight && line.inlandFreight !== 0) ||
          (line.customsClearanceFee && line.customsClearanceFee !== 0) ||
          (line.assemblyFee && line.assemblyFee !== 0) ||
          (line.tax && line.tax !== 0) ||
          (line.portCharge && line.portCharge !== 0) ||
          (line.pdiFee && line.pdiFee !== 0) ||
          (line.commissionFee && line.commissionFee !== 0) ||
          (line.financingFee && line.financingFee !== 0) ||
          (line.otherFee && line.otherFee !== 0)
        );
      });

    if (hasAnyFee && !value) {
      callback(new Error('已填写费用项时币种必选'));
    } else {
      callback();
    }
  }
  // 其他费用变动
  otherFeeChange(index) {
    // 重新校验费用项
    this.$refs.form.validateField('lines.' + index + '.otherFeeName');
    // 触发币种重新验证
    this.triggerCurrencyValidation();
  }
  validatorOtherFeeName(row, value, callback) {
    // 移除强制验证，因为所有费用项已改为非必填
    // 如果其他费用不等于0，其他费用项必填
    if (row.otherFee && row.otherFee != 0) {
      if (!row.otherFeeName) {
        callback('请输入');
      }
    }
    callback();
  }
  // 费用项变动时触发币种重新验证
  triggerCurrencyValidation() {
    this.$refs.form.validateField('supplementPriceCcyId');
  }

  // 获取国际运费值
  getInternationalTransportationValue(row: any, currencyId: string) {
    if (!row.internationalTransportations) return null;
    const item = row.internationalTransportations.find(
      (item: any) => item.currencyId === currencyId
    );
    return item ? item.value : null;
  }

  // 更新国际运费值
  updateInternationalTransportation(row: any, currencyId: string, value: number) {
    if (!row.internationalTransportations) {
      row.internationalTransportations = [];
    }
    let item = row.internationalTransportations.find((item: any) => item.currencyId === currencyId);
    if (!item) {
      const currency = row.currencyIds.find((c: any) => c.currencyId === currencyId);
      item = {
        currencyId: currencyId,
        currencySymbol: currency ? currency.currencySymbol : '',
        value: null,
      };
      row.internationalTransportations.push(item);
    }
    item.value = value;
  }

  // 获取国际运输保险费值
  getInternationalTransportationPremiumValue(row: any, currencyId: string) {
    if (!row.internationalTransportationPremiums) return null;
    const item = row.internationalTransportationPremiums.find(
      (item: any) => item.currencyId === currencyId
    );
    return item ? item.value : null;
  }

  // 更新国际运输保险费值
  updateInternationalTransportationPremium(row: any, currencyId: string, value: number) {
    if (!row.internationalTransportationPremiums) {
      row.internationalTransportationPremiums = [];
    }
    let item = row.internationalTransportationPremiums.find(
      (item: any) => item.currencyId === currencyId
    );
    if (!item) {
      const currency = row.currencyIds.find((c: any) => c.currencyId === currencyId);
      item = {
        currencyId: currencyId,
        currencySymbol: currency ? currency.currencySymbol : '',
        value: null,
      };
      row.internationalTransportationPremiums.push(item);
    }
    item.value = value;
  }
  // 数据过滤,单台费用下明细列空字符串转null
  getParams() {
    let params: SpecialApplySupplementPrice = this.entity;
    params.lines = this.entity.lines.map((item) => {
      for (let key in item) {
        if (item[key] === '') {
          item[key] = null;
        }
      }
      return {
        ...item,
      };
    });
    return params;
  }
  doSave() {
    return SpecialApplyBillApi.saveSupplementPrice(this.getParams());
  }
  doSubmit() {
    // 当有2个tab时，tab2提交前需要校验tab1的特价申请说明
    // 通过父组件缓存的校验状态判断
    // @ts-ignore
    if (typeof this.$parent.checkSpecialApplyDescriptionValid === 'function') {
      // @ts-ignore
      if (!this.$parent.checkSpecialApplyDescriptionValid()) {
        // @ts-ignore
        this.$parent.$parent.$parent.saveFail();
        return;
      }
    }

    let params: any = this.getParams();
    params.outGoing = null;
    params.version = this.baseEntity.version as number;
    if (this.baseEntity.userTaskOutGoings) {
      let findItem = this.baseEntity.userTaskOutGoings.find((item) => {
        return item.key === 'submit';
      });
      if (findItem) {
        params.outGoing = findItem;
      }
    }
    new Dialog(SpecialSubmitDialog, {
      params,
      method: this.submitMethod,
      sucess: () => {
        this.$router.push({
          name: RouterNames.specialApplyList,
        });
      },
      cancel: () => {
        // @ts-ignore
        this.$parent.goStep1();
      },
    }).show();
  }
  submitMethod(params) {
    return SpecialApplyBillApi.saveAndSubmitSupplementPrice(params);
  }
}
