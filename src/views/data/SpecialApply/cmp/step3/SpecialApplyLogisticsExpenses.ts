import { Vue, Component, Prop } from 'vue-property-decorator';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyLogisticsExpensesMatInfo from '@/model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesMatInfo';
import SpecialApplyBillLogisticsExpensesApi from '@/http/price/controller/specialapply/logisticsexpenses/SpecialApplyBillLogisticsExpensesApi';
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType';
import CommonInfoApi from '@/http/data/CommonInfoApi';
import DestinationPortApi from '@/http/data/DestinationPortApi';
import DomesticFreightLandApi from '@/http/data/DomesticFreightLandApi';
import DictsApi from '@/http/basis/DictApi';
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import LogisticsExpensesStep1 from './costEstimate/LogisticsExpensesStep1.vue';
import LogisticsExpensesStep2 from './costEstimate/LogisticsExpensesStep2.vue';

@Component({
  name: 'SpecialApplyLogisticsExpenses',
  components: { SearchRemote, LogisticsExpensesStep1, LogisticsExpensesStep2 },
})
export default class SpecialApplyLogisticsExpenses extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill;

  @Prop({ type: Object, default: new SpecialApplyLogisticsExpensesMatInfo() })
  entity: SpecialApplyLogisticsExpensesMatInfo;

  @Prop({ type: Number, default: 1 })
  activeSubTab: number;

  $refs: any;

  // 枚举引用
  ExportType = ExportType;

  // 子步骤列表
  tabList = [
    { step: 1, title: '物料信息' },
    { step: 2, title: '费用明细' },
  ];

  // 表单校验规则
  rules: any = {
    destinationPortCode: [{ required: true, message: '请选择目的港/目的站', trigger: 'change' }],
    transportTypeId: [{ required: true, message: '请选择国际运输模式', trigger: 'change' }],
    transportTypeRoleId: [{ required: true, message: '请选择运输方式', trigger: 'change' }],
    domesticFreightLandCode: [{ required: true, message: '请选择交货地点', trigger: 'change' }],
  };

  // 运输模式滚装散杂列表
  transportTypeRollList: any[] = [];

  // 获取滚装值
  get oceanRollRollLabel() {
    let findItem = this.transportTypeRollList.find((item) => {
      return item.dictLabel === '海运滚装散杂-滚装';
    });
    if (findItem) {
      return findItem.dictValue;
    }
    return 'oceanRollRoll';
  }

  // 获取散杂值
  get oceanRollScatterLabel() {
    let findItem = this.transportTypeRollList.find((item) => {
      return item.dictLabel === '海运滚装散杂-散杂';
    });
    if (findItem) {
      return findItem.dictValue;
    }
    return 'oceanRollScatter';
  }

  created() {
    // 获取滚装散杂列表
    this.queryTransportRollList();
    // 获取物料信息
    this.getMatInfo();
  }

  // 获取物料信息
  getMatInfo() {
    if (this.baseEntity.id) {
      SpecialApplyBillLogisticsExpensesApi.getMatInfo(this.baseEntity.id)
        .then((res) => {
          if (res.data) {
            // 更新entity数据
            Object.assign(this.entity, res.data);
          }
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    }
  }

  // 获取滚装散杂列表
  queryTransportRollList() {
    DictsApi.getDicts('transport_type_roll', 'zh')
      .then((res) => {
        this.transportTypeRollList = res.data;
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }

  // 目的港/目的站查询
  queryDestinationPort(query: string, page: number) {
    let body = {
      codeLike: null,
      countryEquals: null,
      enabledEquals: 1, // 查询启用状态
      nameEnLike: query,
      nameLike: null,
      pageNum: page,
      pageSize: 20,
    };
    return DestinationPortApi.list(body);
  }

  // 目的港/目的站回显
  queryDestinationPortInit(value: string) {
    let body = {
      codeLike: value,
      countryEquals: null,
      enabledEquals: 1,
      nameEnLike: null,
      nameLike: null,
      pageNum: 1,
      pageSize: 1,
    };
    return DestinationPortApi.list(body);
  }

  // 目的港/目的站值格式化
  destinationPortValueFormat(item: any) {
    return item.code;
  }

  // 目的港/目的站标签格式化
  destinationPortLabelFormat(item: any) {
    return item.nameEn;
  }

  // 国际运输模式查询
  queryTransportList(query, page) {
    let par = {
      codeOrNameLike: query,
      dictTypeEquals: 'transport_type',
      lang: this.$i18n.locale,
      pageNum: page,
      pageSize: 20,
    };
    return CommonInfoApi.currencyList(par);
  }

  // 国际运输模式回显方法
  queryTransportInit(value) {
    let paramIn = {
      codeOrNameLike: value,
      dictTypeEquals: 'transport_type',
      lang: this.$i18n.locale,
      pageNum: 1,
      pageSize: 1,
    };
    return CommonInfoApi.currencyList(paramIn);
  }

  // 国际运输模式值格式化
  transportValueFormat(item: any) {
    return item.dictValue;
  }

  // 国际运输模式标签格式化
  transportLabelFormat(item: any) {
    return item.dictLabel;
  }

  // 目的港/目的站变化
  destinationPortChange(value: any) {
    if (value) {
      this.entity.destinationPortName = value.nameEn;
    } else {
      this.entity.destinationPortName = null;
    }
  }

  // 国际运输模式变动
  transportTypeIdChange(val) {
    // 当不是"海运滚装散杂"时，清空二级选择
    if (!(val && val.dictValue === 'oceanRoll')) {
      this.entity.transportTypeRoleId = null;
    }
  }

  // 滚装散杂变动
  transportTypeRoleIdChange(val) {}

  // 交货地点查询
  queryDomesticFreightLand(query: string, page: number) {
    let body = {
      province: null,
      code: null,
      name: query,
      enabled: 1,
      pageNum: page,
      pageSize: 20,
    };
    return DomesticFreightLandApi.list(body);
  }

  // 交货地点回显
  queryDomesticFreightLandInit(value: string) {
    let body = {
      province: null,
      code: value,
      name: null,
      enabled: 1,
      pageNum: 1,
      pageSize: 1,
    };
    return DomesticFreightLandApi.list(body);
  }

  // 交货地点值格式化
  domesticFreightLandValueFormat(item: any) {
    return item.code;
  }

  // 交货地点标签格式化
  domesticFreightLandLabelFormat(item: any) {
    return item.name;
  }

  // 交货地点变化
  domesticFreightLandChange(value: any) {
    if (value) {
      this.entity.domesticFreightLandName = value.name;
    } else {
      this.entity.domesticFreightLandName = null;
    }
  }

  // 子步骤切换
  async handleStepTabClick(step: number) {
    if (step === this.activeSubTab) {
      return;
    }

    try {
      // 先保存当前步骤
      await this.doSaveCurrentStep();
      // 通知父组件切换步骤
      this.$emit('step-change', step);
    } catch (error) {
      console.error('保存失败，无法切换步骤:', error);
      this.$message.error((error as any).message || '保存失败');
    }
  }

  // 保存当前步骤
  async doSaveCurrentStep() {
    // step1要校验父组件表单
    if (this.activeSubTab === 1) {
      const parentComponent: any = this.$parent.$parent.$parent.$parent.$parent;
      if (parentComponent && parentComponent.validateForm) {
        const valid = await parentComponent.validateForm();
        if (!valid) {
          throw new Error('存在必填项未填写，请填写后再保存');
        }
      }
    }

    // 校验该组件表单
    const valid = await this.validateForm();
    if (!valid) {
      throw new Error('存在必填项未填写，请填写后再保存');
    }

    // 校验并保存当前子步骤
    const currentRef = this.getCurrentStepRef();
    if (currentRef && currentRef.doSave) {
      await currentRef.doSave();
    }
  }

  // 获取当前步骤组件引用
  getCurrentStepRef() {
    return this.$refs[`logisticsStep${this.activeSubTab}`];
  }

  // 表单校验
  validateForm(): Promise<boolean> {
    return new Promise((resolve) => {
      this.$refs.form.validate((valid: boolean) => {
        resolve(valid);
      });
    });
  }

  // 保存整个模块
  async doSave() {
    return await this.doSaveCurrentStep();
    // return SpecialApplyBillLogisticsExpensesApi.saveMatInfo(this.entity);
  }

  refreshData() {
    this.getMatInfo();
  }
}
