<template>
  <div class="special-apply-other-expenses">
    <!-- 左侧tab -->
    <div class="step4-main">
      <div class="step4-main-tab">
        <div
          :class="['step4-main-tab-item', { 'step4-main-tab-active': activeSubTab === item.step }]"
          v-for="item in tabList"
          :key="item.step"
          @click="handleStepTabClick(item.step)"
        >
          <div class="item-icon">
            <div>{{ item.step }}</div>
          </div>
          <div class="item-text">{{ item.name }}</div>
        </div>
      </div>
      <div class="step4-main-content">
        <OtherExpensesStep1
          v-if="activeSubTab === 1"
          :key="`other-step1-${baseEntity.id}`"
          ref="otherStep1"
          :baseEntity="baseEntity"
          :parentEntity="entity"
          @refresh="handleRefresh"
        />
        <OtherExpensesStep2
          v-if="activeSubTab === 2"
          :key="`other-step2-${baseEntity.id}`"
          ref="otherStep2"
          :baseEntity="baseEntity"
          :parentEntity="entity"
        />
        <OtherExpensesStep3
          v-if="activeSubTab === 3"
          :key="`other-step3-${baseEntity.id}`"
          ref="otherStep3"
          :baseEntity="baseEntity"
          :parentEntity="entity"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" src="./SpecialApplyOtherExpenses.ts"></script>

<style lang="scss" scoped>
.special-apply-other-expenses {
  .step4-main {
    flex: 1;
    display: flex;
    border-radius: 4px;
    border: 1px solid #d7dfeb;
    margin-bottom: 12px;

    &-tab {
      flex-shrink: 0;
      flex-grow: 0;
      width: 160px;
      color: #79879e;
      background-color: #f7f9fc;

      &-item {
        display: flex;
        align-items: center;
        height: 56px;
        padding: 0px 12px;
        cursor: pointer;

        .item-icon {
          width: 20px;
          height: 20px;
          line-height: 20px;
          text-align: center;
          border-radius: 10px;
          background-color: #e5e8eb;
          margin-right: 12px;
        }

        .item-text {
          font-size: 14px;
        }
      }

      &-active {
        background-color: #fff;

        .item-icon {
          font-weight: bold;
          color: #fff;
          background-color: $--color-primary;
        }

        .item-text {
          font-weight: bold;
          color: $--color-primary;
        }
      }
    }

    &-content {
      width: 0px;
      flex-grow: 1;
      flex-shrink: 1;
      padding: 12px;
    }
  }
}
</style>
