import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import CommonInfoApi from '@/http/data/CommonInfoApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
import SpecialApplyBillLogisticsExpensesApi from '@/http/price/controller/specialapply/logisticsexpenses/SpecialApplyBillLogisticsExpensesApi';

// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import ApplyDescription from '@/model/remote/price/api/specialapply/bill/ApplyDescription';
import { SpecialType } from '@/model/remote/price/api/specialapply/bill/SpecialType';
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType';
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType';
import SpecialApplyLogisticsExpensesMatInfo from '@/model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesMatInfo';
// COMPONENTES
import Panel from '@/components/panel/Panel.vue';
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import FormItem from '@/components/form/form-item/index.vue';
import Editor from '@/components/editor/editor.vue';
// 费用估算子组件
import SpecialApplyLogisticsExpenses from './SpecialApplyLogisticsExpenses.vue';
import SpecialApplyOtherExpenses from './SpecialApplyOtherExpenses.vue';

@Component({
  name: 'SpecialApplyCostEstimate',
  components: {
    Panel,
    SearchRemote,
    NumberInput,
    FormItem,
    Editor,
    SpecialApplyLogisticsExpenses,
    SpecialApplyOtherExpenses,
  },
})
export default class SpecialApplyCostEstimate extends Vue {
  @Prop({ type: Boolean, default: false }) value;
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  @Prop({ type: Object, default: new ApplyDescription() })
  specialApplyDescription: ApplyDescription;
  $refs: any;
  // 物流费用实体数据
  logisticsEntity: SpecialApplyLogisticsExpensesMatInfo = new SpecialApplyLogisticsExpensesMatInfo();
  importLoading: boolean = false;
  rules: any = {
    incotermsId: [{ required: true, message: '请选择贸易术语', trigger: 'change' }],
  };
  activeName: string = 'LogisticsExpenses'; // 当前选中tab

  // 枚举引用
  ExportType = ExportType;
  GuidePriceType = GuidePriceType;

  PrecisionUtil = PrecisionUtil;
  ObjectUtil = ObjectUtil;
  activeSubTab: number = 1; // 当前选中子tab

  otherTabList: any[] = [
    {
      step: 1,
      name: '填写费用',
    },
    {
      step: 2,
      name: '付款方式',
    },
    {
      step: 3,
      name: '费用汇总',
    },
  ];

  // 获取当前顶部模块tab对应的子tab列表
  get currentTabList() {
    return this.otherTabList;
  }

  // 是否显示"保存并关闭"、"取消并关闭"按钮
  get showSaveButtons() {
    // 物流费用-子步骤1、其他费用-子步骤1、其他费用-子步骤2 显示保存按钮
    return (
      (this.activeName === 'LogisticsExpenses' && this.activeSubTab === 1) ||
      (this.activeName === 'OtherExpenses' && (this.activeSubTab === 1 || this.activeSubTab === 2))
    );
  }

  // 是否显示"确认并应用"、"关闭"按钮
  get showApplyButtons() {
    // 物流费用-子步骤2、其他费用-子步骤3 显示应用按钮
    return (
      (this.activeName === 'LogisticsExpenses' && this.activeSubTab === 2) ||
      (this.activeName === 'OtherExpenses' && this.activeSubTab === 3)
    );
  }

  get incotermsOptions() {
    // 自营-买断：CIF、FOB
    if (
      this.baseEntity.exportTypeId === ExportType.self &&
      this.baseEntity.saleMode === GuidePriceType.buyOut
    ) {
      return [
        {
          value: 'CIF',
          label: 'CIF',
        },
        {
          value: 'FOB',
          label: 'FOB',
        },
      ];
    }
    // 自营-提佣：EXW
    if (
      this.baseEntity.exportTypeId === ExportType.self &&
      this.baseEntity.saleMode === GuidePriceType.commission
    ) {
      return [{ value: 'EXW', label: 'EXW' }];
    }
  }

  get localValue() {
    return this.value;
  }
  set localValue(val) {
    this.$emit('input', val);
  }
  // 赋值
  doValue(val: any) {
    // 初始化物流费用数据
    this.initLogisticsData();
  }

  // 初始化物流费用数据
  async initLogisticsData() {
    if (this.baseEntity.id) {
      try {
        const res = await SpecialApplyBillLogisticsExpensesApi.getMatInfo(this.baseEntity.id);
        if (res.data) {
          this.logisticsEntity = res.data;
        } else {
          this.logisticsEntity = new SpecialApplyLogisticsExpensesMatInfo();
        }
      } catch (error) {
        console.error('获取物流费用数据失败:', error);
        this.logisticsEntity = new SpecialApplyLogisticsExpensesMatInfo();
      }
    } else {
      this.logisticsEntity = new SpecialApplyLogisticsExpensesMatInfo();
    }

    // 为自营-提佣场景设置默认EXW贸易术语
    if (
      this.baseEntity.exportTypeId === ExportType.self &&
      this.baseEntity.saleMode === GuidePriceType.commission &&
      !this.logisticsEntity.incotermsId
    ) {
      this.logisticsEntity.incotermsId = 'EXW';
    }
  }

  // 币种查询
  queryCurrencyList(query, page) {
    let par = {
      codeOrNameLike: query,
      dictTypeEquals: 'ccy',
      lang: this.$i18n.locale,
      pageNum: page,
      pageSize: 20,
    };
    return CommonInfoApi.currencyList(par);
  }

  // 保存数据（不关闭弹窗）
  async doSaveData() {
    // 先校验父组件表单
    const valid = await this.validateForm();
    if (!valid) {
      throw new Error('存在必填项未填写，请填写后再保存');
    }

    // 根据当前活跃的tab和子步骤调用对应的子组件保存方法
    const currentRef = this.getCurrentSubComponentRef();
    if (!currentRef) {
      console.error('未找到对应的子组件');
      throw new Error('保存失败');
    }

    await currentRef.doSave();
  }

  // 表单校验方法
  validateForm(): Promise<boolean> {
    return new Promise((resolve) => {
      this.$refs.form.validate((valid: boolean) => {
        resolve(valid);
      });
    });
  }

  // 获取当前活跃的子组件引用
  getCurrentSubComponentRef() {
    if (this.activeName === 'LogisticsExpenses') {
      // 物流费用模块 - 使用新的组件
      return this.$refs.logisticsExpenses;
    } else if (this.activeName === 'OtherExpenses') {
      // 其他费用模块 - 使用新的组件
      return this.$refs.otherExpenses;
    }
    return null;
  }

  // 保存并关闭
  async handleConfirm() {
    try {
      await this.doSaveData();
      this.$emit('confirm');
      this.localValue = false;
    } catch (error) {
      this.$message.error((error as any).message || '保存失败');
    }
  }

  // 确认并应用
  async handleApply() {
    try {
      this.importLoading = true;
      await SpecialApplyBillApi.confirmApplyPriceOverview(
        this.baseEntity.id!,
        this.baseEntity.version!
      );
      // 成功后直接回填数据至主页面并关闭弹窗
      this.localValue = false;
      this.$emit('confirm');
    } catch (error) {
      console.error('确认并应用失败:', error);
      this.$message.error((error as any).message || '确认并应用失败');
    } finally {
      this.importLoading = false;
    }
  }

  // 关闭（不保存）
  handleCloseOnly() {
    this.localValue = false;
  }

  // 关闭抽屉
  handleClose() {
    this.$confirm('确认关闭？')
      .then((_) => {
        // 清除校验
        this.$refs.form.clearValidate();
        this.localValue = false;
      })
      .catch((_) => {});
  }
  @Watch('localValue', { deep: true })
  localValueChange(val) {
    if (val) {
      this.doValue(this.specialApplyDescription);
    }
  }

  // 左侧步骤tab切换（保存并切换）
  async handleStepTabClick(step: number) {
    if (step === this.activeSubTab) {
      return; // 点击当前tab，不处理
    }

    try {
      // 先保存当前步骤的数据（不关闭弹窗）
      await this.doSaveData();
      // 保存成功后切换tab
      this.activeSubTab = step;
    } catch (error) {
      // 保存失败，不切换tab
      console.error('保存失败，无法切换tab:', error);
      this.$message.error((error as any).message || '保存失败');
    }
  }

  // tab切换前的钩子，用于保存数据
  async beforeTabLeave(activeName: string, oldActiveName: string) {
    if (activeName === oldActiveName) {
      return Promise.reject(); // 相同tab，不允许切换
    }

    try {
      // 先保存当前数据
      await this.doSaveData();
      // 保存成功后重置子tab并刷新
      this.handleRefresh();
      this.activeSubTab = 1;
      return true; // 允许切换
    } catch (error) {
      // 保存失败，阻止切换
      console.error('保存失败，无法切换tab:', error);
      this.$message.error((error as any).message || '保存失败');
      return Promise.reject(error); // 阻止切换
    }
  }

  // 处理物流费用步骤变化
  handleLogisticsStepChange(step: number) {
    this.activeSubTab = step;
    // 步骤切换后刷新baseEntity
    this.handleRefresh();
  }

  // 处理其他费用步骤变化
  handleOtherStepChange(step: number) {
    this.activeSubTab = step;
    // 步骤切换后刷新baseEntity
    this.handleRefresh();
  }

  // 处理刷新事件 - 通知父组件刷新baseEntity
  handleRefresh() {
    // 发出刷新事件，让TabStep1处理
    this.$emit('refresh');
  }

  // 贸易术语变更处理
  async handleIncotermsChange(newValue: string) {
    // 记录当前值作为旧值
    const oldValue = this.logisticsEntity.incotermsId;
    // 如果值没有变化，不处理
    if (newValue === oldValue) {
      return;
    }
    // 如果旧值为空，直接赋值
    if (!oldValue) {
      this.logisticsEntity.incotermsId = newValue;
      return;
    }

    try {
      await this.$confirm(
        '切换贸易术语将清除已填费用信息，您需要重新填写<br/><br/>确定要继续吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
        }
      );

      // 用户确认，更新贸易术语并清空所有相关数据
      this.logisticsEntity.incotermsId = newValue;
      this.clearLogisticsData();
    } catch (error) {
      // 用户取消，不做任何操作，保持原值
      return;
    }
  }

  // 清空物流费用相关数据
  clearLogisticsData() {
    // 清空logisticsEntity的部分数据
    this.logisticsEntity.destinationPortCode = null;
    this.logisticsEntity.destinationPortName = null;
    this.logisticsEntity.transportTypeId = null;
    this.logisticsEntity.transportTypeRoleId = null;
    this.logisticsEntity.domesticFreightLandCode = null;
    this.logisticsEntity.domesticFreightLandName = null;
    this.logisticsEntity.dmTransportTypeId = null;

    // 清空物流费用组件Step1的用户填写数据
    const logisticsRef = this.$refs.logisticsExpenses;
    if (logisticsRef && logisticsRef.getCurrentStepRef) {
      const currentStepRef = logisticsRef.getCurrentStepRef();
      if (currentStepRef && currentStepRef.clearData) {
        currentStepRef.clearData();
      }
    }
  }
}
