<template>
  <el-dialog
    title="汇率表格"
    :visible.sync="dialogVisible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="exchange-rate-content">
      <el-table
        :data="getTableData()"
        border
        :show-header="false"
        class="exchange-rate-table"
        :row-class-name="getRowClassName"
      >
        <el-table-column
          prop="sourceCcy"
          min-width="120"
          align="center"
          class-name="source-currency-column"
        >
        </el-table-column>
        <el-table-column
          v-for="currency in exchangeRateData.targetCurrencyIds"
          :key="currency.currencyId"
          min-width="120"
          align="center"
        >
          <template #default="scope">
            {{ scope.row[currency.currencyId] || '--' }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script src="./ExchangeRateDialog.ts"></script>

<style lang="scss" scoped>
.exchange-rate-content {
  .exchange-rate-table {
    /deep/ .source-currency-column {
      background-color: #f5f7fa !important;
      font-weight: 500;
    }

    /deep/ .el-table__body-wrapper {
      .el-table__row {
        .source-currency-column {
          background-color: #f5f7fa !important;
        }

        // 表头行样式 - 只对第一列保持特殊样式，其他列恢复正常
        &.header-row {
          font-weight: 500;

          // 只有第一列（源币种列）保持灰色背景
          .source-currency-column {
            background-color: #f5f7fa !important;
          }

          // 其他列恢复白色背景
          td:not(.source-currency-column) {
            background-color: #ffffff !important;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: center;
}

/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
</style>
