import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
// MODELS
import SpecialApplyCurrencyExchangeRate from '@/model/remote/price/api/specialapply/bill/SpecialApplyCurrencyExchangeRate';

@Component({
  name: 'ExchangeRateDialog',
})
export default class ExchangeRateDialog extends Vue {
  @Prop({ type: Boolean, default: false })
  visible: boolean;

  @Prop({ type: Object, default: () => new SpecialApplyCurrencyExchangeRate() })
  exchangeRateData: SpecialApplyCurrencyExchangeRate;

  dialogVisible: boolean = false;

  @Watch('visible')
  onVisibleChange(newVal: boolean) {
    this.dialogVisible = newVal;
  }

  @Watch('dialogVisible')
  onDialogVisibleChange(newVal: boolean) {
    if (!newVal) {
      this.$emit('update:visible', false);
    }
  }

  // 关闭弹窗
  handleClose() {
    this.dialogVisible = false;
    this.$emit('close');
  }

  // 获取行的样式类名
  getRowClassName({ rowIndex }: { rowIndex: number }) {
    return rowIndex === 0 ? 'header-row' : '';
  }

  // 获取表格数据
  getTableData() {
    const sourceMap = new Map<string, any>();

    // 按源币种分组
    this.exchangeRateData.exchangeRateList.forEach((rate) => {
      if (rate.sourceCcy && rate.targetCcy) {
        if (!sourceMap.has(rate.sourceCcy)) {
          sourceMap.set(rate.sourceCcy, { sourceCcy: rate.sourceCcy });
        }
        const sourceRow = sourceMap.get(rate.sourceCcy);
        if (sourceRow) {
          sourceRow[rate.targetCcy] = rate.exchangeRate;
        }
      }
    });

    const dataRows = Array.from(sourceMap.values());

    // 添加表头行
    const headerRow: any = { sourceCcy: '源币种' };
    this.exchangeRateData.targetCurrencyIds.forEach((currency) => {
      headerRow[currency.currencyId!] = `折${currency.currencyId}汇率`;
    });

    // 将表头行放在第一行
    return [headerRow, ...dataRows];
  }
}
