<!--
 * @Author: 张文轩
 * @Date: 2023-12-22 14:44:25
 * @LastEditTime: 2024-08-23 15:50:31
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step4\SelectExtended.vue
 * 记得注释
-->
<template>
  <el-dialog :title="'查看' + childrenTitle" width="65%" :visible.sync="localValue" append-to-body>
    <el-table ref="table" row-key="policyId" :data="childrenEntity" max-height="450px">
      <el-table-column label="子公司" minWidth="100">
        <template #default="scoped">
          <div>{{ scoped.row.applicationScop.sbsdyName }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="办事处"
        prop="matDesc"
        minWidth="100"
        v-if="baseEntity.specialType != 'adjustZoneGuidePrice'"
      >
        <template #default="scoped">
          <div>{{ scoped.row.applicationScop.ofcName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="销往国" minWidth="100">
        <template #default="scoped">
          {{ formatCtryName(scoped.row.applicationScop) }}
        </template>
      </el-table-column>
      <el-table-column
        label="客户"
        minWidth="100"
        v-if="baseEntity.specialType == 'importantProjects'"
      >
        <template #default="scoped">
          <div>{{ scoped.row.applicationScop.custName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="币种" minWidth="100">
        <template #default="scoped">
          {{ scoped.row.applicationScop.currencyId }}
        </template>
      </el-table-column>
      <el-table-column
        label="远期贴现费率"
        minWidth="100"
        v-if="childrenType == 'occForwardDiscountRate'"
      >
        <template #default="scoped">
          <div v-if="scoped.row.occForwardDiscountRate">
            {{ (Math.abs(scoped.row.occForwardDiscountRate) * 100) | numberFilter('--', 2, true) }}%
          </div>
          <div v-else style="color: #f56c6c">未获取到</div>
        </template>
      </el-table-column>
      <el-table-column
        :label="childrenTitle"
        minWidth="100"
        v-if="childrenType == 'forwardDiscount' || childrenType == 'premium'"
      >
        <template #default="scoped">
          <div v-if="scoped.row.amount.value !== null" style="display: flex">
            <div>
              {{ scoped.row.amount.currencySymbol }}
            </div>
            <div>
              {{ Math.abs(scoped.row.amount.value) }}
              <!-- <template v-if="childrenType == 'forwardDiscount'">
                {{ Math.round(Math.abs(scoped.row.amount.value)) }}
              </template>
              <template v-else>
                {{ Math.abs(scoped.row.amount.value) | numberFilter('--', 2, true) }}
              </template> -->
            </div>
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column>
      <el-table-column label="信保费率" minWidth="100" v-if="childrenType == 'occPremiumRate'">
        <template #default="scoped">
          <div v-if="scoped.row.subsidiary">
            <!-- {{ (Math.abs(scoped.row.occPremiumRate) * 100) | numberFilter('--', 2, true) }}% -->
            --
          </div>
          <!-- style="color: #f56c6c" -->
          <div v-else>
            <div v-if="scoped.row.calPremiumRate && days != 0">
              <div v-if="scoped.row.occPremiumRate === null" style="color: #f56c6c">未获取到</div>
              <div v-else>
                {{ (Math.abs(scoped.row.occPremiumRate) * 100) | numberFilter('--', 2, true) }}%
              </div>
            </div>
            <div v-else>--</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="延保费/台" minWidth="100" v-if="childrenType == 'extendedInfos'">
        <template #default="scoped">
          <div v-if="scoped.row.total" style="display: flex">
            <div>
              {{ scoped.row.applicationScop.currencySymbol }}
            </div>
            <div>
              {{ Math.abs(scoped.row.total) }}
            </div>
          </div>
          <div v-else style="display: flex">
            {{ scoped.row.applicationScop.currencySymbol }}
            <div>0</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="国内运输保险费/台"
        minWidth="100"
        v-if="childrenType == 'domesticTransportationInsurance'"
      >
        <template #default="scoped">
          <div v-if="scoped.row.amount && scoped.row.amount.value !== null" style="display: flex">
            <div>
              {{ scoped.row.amount.currencySymbol }}
            </div>
            <div>
              {{ Math.abs(scoped.row.amount.value) | numberFilter('--', 2, true) }}
            </div>
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="localValue = !localValue">取消</el-button>
      <el-button size="mini" type="primary" @click="localValue = !localValue"> 确定 </el-button>
    </div>
  </el-dialog>
</template>

<script src="./CostEstimateDialogCheck.ts"></script>

<style lang="scss" scoped>
/deep/ .el-dialog__header {
  padding: 10px 20px;
  font-size: 14px;
  color: #242633;
  line-height: 22px;
  .el-dialog__headerbtn {
    top: 10px;
  }
}
/deep/ .el-dialog__body {
  padding-top: 16px;
}
/deep/ .el-table__cell {
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .cell {
  padding: 0px 12px;
}
.el-form-item {
  margin-bottom: 12px;
}
</style>
