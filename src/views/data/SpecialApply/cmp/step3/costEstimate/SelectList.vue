<template>
  <lg-dialog title="选择特价申请" width="60%">
    <el-form ref="form" size="mini" :model="filter">
      <el-row :gutter="24" style="display: flex; flex-wrap: wrap">
        <el-col :span="8">
          <form-item label="主题" prop="topicLike">
            <el-input v-model="filter.topicLike" placeholder=""></el-input>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="单号" prop="idStart">
            <el-input v-model="filter.idStart" placeholder=""></el-input>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="状态" prop="stateEq">
            <el-select v-model="filter.stateEq" style="width: 100%">
              <el-option
                v-for="item in stateList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="审批节点" prop="processNodeNameEq">
            <el-select v-model="filter.processNodeNameEq" style="width: 100%">
              <el-option
                v-for="item in approveNodeIdList"
                :key="item.code"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="子公司" prop="sbsdyIdEq">
            <search-remote
              v-model="filter.sbsdyIdEq"
              placeholder="输入搜索或选择"
              valueKey="sbsdyId"
              :queryMethod="querySideCompanyList"
              :valueFormat="sbsyValueFormat"
              :labelFormat="sbsyLabelFormat"
              :multiple="false"
              :lazyLoad="true"
            ></search-remote>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="办事处" prop="ofcIdEq">
            <search-remote
              v-model="filter.ofcIdEq"
              placeholder="输入搜索或选择"
              valueKey="officeId"
              :queryMethod="queryOfficeList"
              :valueFormat="officeValueFormat"
              :labelFormat="officeLabelFormat"
              :multiple="false"
              :lazyLoad="true"
            ></search-remote>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="销往国" prop="ctryIdEq">
            <search-remote
              v-model="filter.ctryIdEq"
              placeholder="输入搜索或选择"
              valueKey="ctryIdStr"
              :queryMethod="queryCountryList"
              :valueFormat="countryValueFormat"
              :labelFormat="countryLabelFormat"
              :multiple="false"
              :lazyLoad="true"
            ></search-remote>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="客户" prop="custIdEq">
            <search-remote
              v-model="filter.custIdEq"
              placeholder="输入搜索或选择"
              valueKey="custIdStr"
              :queryMethod="queryCustomerList"
              :valueFormat="customValueFormat"
              :labelFormat="cusomeLabelFormat"
              :multiple="false"
              :lazyLoad="true"
            ></search-remote>
          </form-item>
        </el-col>
        <el-col :span="8">
          <form-item label="">
            <el-button size="mini" type="primary" @click="doSearch">查询</el-button>
            <el-button size="mini" @click="doReset">重置</el-button>
          </form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-table :data="tableData" v-loading="loading" max-height="350px" @row-click="rowClick">
      <el-table-column width="50">
        <template #default="scope">
          <table-single-radio
            item-key="id"
            v-model="singleSelected"
            :label="scope.row"
            :selectable="checkSingleSelectable"
            :index="scope.$index"
          >
          </table-single-radio>
        </template>
      </el-table-column>
      <el-table-column label="申请主题">
        <template #default="scoped">
          <div class="topic-cell">
            <div class="topic-cell-num">
              <i v-if="scoped.row.urgentApprove" class="iconfont icon-hang1_icon" />
              <span>{{ scoped.row.id }}</span>
            </div>
            <div v-ellipsis>{{ scoped.row.topic }}</div>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column label="销售组织">
        <template #default="scoped">
          <div v-ellipsis>{{ scoped.row.sbsdyName | text }}</div>
          <div v-ellipsis>{{ scoped.row.ofcName | text }}</div>
        </template>
      </el-table-column>
      <el-table-column label="报价客户">
        <template #default="scoped">
          <div
            v-if="scoped.row.custCode"
            :title="`[${scoped.row.custCode}]${scoped.row.custName}`"
            class="ellipsis-two-line"
          >
            {{ `[${scoped.row.custCode}]${scoped.row.custName}` }}
          </div>
          <div v-else>--</div>
        </template>
      </el-table-column> -->

      <el-table-column label="特价类型">
        <template #default="scoped">
          <div v-if="scoped.row.specialType == 'sampleMachine'">样机测试/推广</div>
          <div v-if="scoped.row.specialType == 'importantProjects'">重点项目/客户特价</div>
          <div v-if="scoped.row.specialType == 'adjustZoneGuidePrice'">区域指导价调整</div>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template #default="scoped">
          <StatePopover
            :state="scoped.row.state | stateFilter"
            :showIcon="scoped.row.state !== 'draft'"
            :searchMethod="() => queryHistoryTask(scoped.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="审批节点">
        <template #default="scoped">
          <!-- <div v-if="scoped.row.taskState">{{ scoped.row.taskState }}</div>
          <div v-else>{{ scoped.row.state | stateFilter }}</div> -->
          <div>{{ scoped.row.taskState | text }}</div>
        </template>
      </el-table-column>
      <el-table-column label="有效期" min-width="150">
        <template #default="scoped">
          <div class="ellipsis-two-line">{{ scoped.row.beginDate }}～{{ scoped.row.endDate }}</div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      small
      class="pagination"
      layout="total, prev, pager, next,sizes"
      :current-page.sync="paging.pageNum"
      :page-size="paging.pageSize"
      :page-sizes="[10, 20, 30, 50]"
      :pager-count="5"
      :total="total"
      @current-change="doPageChange"
      @size-change="doSizeChange"
    >
    </el-pagination>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleCancel">取消</el-button>
      <el-button size="mini" type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </lg-dialog>
</template>

<script src="./SelectList.ts"></script>

<style lang="scss" scoped>
/deep/ .el-dialog__header {
  padding: 10px 20px;
  font-size: 14px;
  color: #242633;
  line-height: 22px;
}
/deep/ .el-dialog__body {
  padding-top: 16px;
}
.el-form-item {
  margin-bottom: 12px;
}
.topic-cell {
  padding: 8px 0px;
  &-num {
    display: flex;
    align-items: center;
    height: 18px;
    line-height: 18px;
    i {
      color: #de3232;
      font-size: 18px;
      margin-right: 4px;
    }
  }
}
.pagination {
  flex-grow: 0;
  margin-top: 10px;
  margin-bottom: 10px;

  text-align: right;
  white-space: initial;
  ::v-deep .el-pagination__total {
    color: #79879e;
  }
}
/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
</style>
