import { Vue, Component, Prop } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import PaymentTypeGroupApi from '@/http/price/controller/paymenttypegroup/PaymentTypeGroupApi';
import SpecialApplyBillOtherExpenesApi from '@/http/price/controller/specialapply/otherexpenses/SpecialApplyBillOtherExpenesApi';
// MODELS
import SpecialApplyPayTypeSaveRequest from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyPayTypeSaveRequest';
import SpecialApplyApplicationScopePayType from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyApplicationScopePayType';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import ApplicationScopeExpenseRateCalRequest from '@/model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRateCalRequest';
import ApplicationScopeExpenseRate from '@/model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRate';
// COMPONENTES
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import Dialog from '@/components/dialog/Dialog';
import DialogCheck from '@/views/data/SpecialApply/cmp/drawer/DialogCheck.vue';

@Component({
  name: 'OtherExpensesStep2',
  components: { SearchRemote, NumberInput },
})
export default class OtherExpensesStep2 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  $refs: any;
  loading: boolean = false;
  height: string = '400px';
  paymentTypeList: any[] = []; // 付款方式级联列表
  entity: SpecialApplyApplicationScopePayType[] = [];
  ObjectUtil = ObjectUtil;
  PrecisionUtil = PrecisionUtil;
  cascaderProps: any = {
    expandTrigger: 'hover',
    children: 'lines',
    label: 'nameEn',
    value: 'id',
    emitPath: false, // 只返回选中节点的value
  };
  form: SpecialApplyPayTypeSaveRequest = new SpecialApplyPayTypeSaveRequest();
  rules = {};
  ruleForm = {
    paymentTypeId: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
    percentage: [{ required: true, message: '请填写付款比例', trigger: 'change' }],
  };
  days: number = 0;

  // 是否展示添加按钮
  get showAddBtn() {
    let total = 0;
    this.form.lines.forEach((item) => {
      total += Number(item.percentage) || 0;
    });
    if (total >= 100) {
      return false;
    }
    return true;
  }
  created() {
    // 根据出口类型获取全部付款方式
    PaymentTypeGroupApi.listAllPaymentType(this.baseEntity.exportTypeId as any)
      .then((res) => {
        if (res.data) {
          this.paymentTypeList = res.data;
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
    this.loading = true;
    // 查询付款信息
    SpecialApplyBillOtherExpenesApi.getPayType(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.entity = res.data || [];
          this.form.lines = this.entity.map((item) => {
            return {
              ...item,
              percentage: ObjectUtil.isNullOrBlank(item.percentage)
                ? null
                : PrecisionUtil.floatMul(item.percentage, 100),
            };
          });
          this.entity.forEach((item, index) => {
            this.paymentTypeChange(item, index);
          });
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  mounted() {
    this.resizeHandler();
    window.addEventListener('resize', this.resizeHandler);
  }
  // 获取付款比例最大值
  getRatioMax(index) {
    let total = 0;
    this.form.lines.forEach((item, i) => {
      if (i !== index) {
        total += Number(item.percentage) || 0;
      }
    });
    if (total >= 100) {
      return 0;
    }
    return Number(PrecisionUtil.floatSub(100, total));
  }
  // 添加付款方式
  doAdd() {
    this.form.lines.push(new SpecialApplyApplicationScopePayType());
  }
  // 删除
  doDelete(index) {
    this.form.lines.splice(index, 1);
  }
  // 付款方式变动
  paymentTypeChange(row, index) {
    let paymentType: any = {};
    this.paymentTypeList.find((i) => {
      i.lines.find((j) => {
        if (j.id === row.paymentTypeId) {
          paymentType = j;
        }
      });
    });
    row.paymentTypeName = paymentType.name; // 中文名
    row.paymentTypeNameEn = paymentType.nameEn; // 英文名
    row.allowedForward = paymentType.payForward === 1 ? true : false; // 是否存在远期
    if (row.allowedForward) {
      if (!ObjectUtil.isNullOrBlank(row.days)) {
        this.calCreditForwardRate(row, index);
      }
    } else {
      // 重置行
      row.days = null;
      row.applicationScopeExpenseRate = [];
    }
  }
  // 天数是否可编辑
  daysDisabled(row) {
    this.paymentTypeList.find((item) => {
      return item.dictValue === row.paymentTypeId;
    });
    return true;
  }
  // 天数变动
  daysChange(row, index) {
    // 清空特殊信保费率
    row.applyCreditInsuranceRate = null;
    this.calCreditForwardRate(row, index);
  }
  // 计算远期贴现率和信保费率
  calCreditForwardRate(row, index) {
    let params: ApplicationScopeExpenseRateCalRequest = new ApplicationScopeExpenseRateCalRequest();
    params.paymentTypeId = row.paymentTypeId;
    params.days = row.days;
    params.owner = this.baseEntity.id;
    params.allowedForward = row.allowedForward;
    this.loading = true;
    SpecialApplyBillOtherExpenesApi.calRate(params)
      .then((res) => {
        if (res.data) {
          this.form.lines[index].applicationScopeExpenseRate = res.data;
        }
      })
      .catch((rej) => {
        this.$refs.table.doLayout();
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  async doSave(model) {
    let arr: any = [];
    this.form.lines.forEach((item) => {
      let str = (item.paymentTypeId || '-') + (item.percentage || '-') + item.days;
      arr.push(str);
    });
    if ([...new Set(arr)].length !== arr.length) {
      return new Promise((resolve, reject) => {
        reject(new Error('支付方式+付款比例+天数不允许存在重复行'));
      });
    }
    if (this.form.lines.length <= 0) {
      return new Promise((resolve, reject) => {
        reject(new Error('支付方式行至少存在一条'));
      });
    }
    if (await this.onSave()) {
      let params: SpecialApplyPayTypeSaveRequest = new SpecialApplyPayTypeSaveRequest();
      params.owner = this.baseEntity.id as string;
      params.lines = this.form.lines.map((item, index) => {
        return {
          ...item,
          id: item.id,
          owner: item.owner,
          paymentTypeId: item.paymentTypeId,
          percentage: ObjectUtil.isNullOrBlank(item.percentage)
            ? null
            : PrecisionUtil.floatDiv(item.percentage, 100),
          days: item.days,
          applicationScopeExpenseRate: item.applicationScopeExpenseRate,
          allowedForward: item.allowedForward,
        };
      });
      params.check = true;
      params.version = this.baseEntity.version as number;

      return SpecialApplyBillOtherExpenesApi.savePayType(params);
    }
  }
  onSave() {
    return this.$refs['form'].validate();
  }
  resizeHandler() {
    this.height = window.innerHeight - 262 + 'px';
  }
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler);
  }
  // 查看远期贴现率和信保费率
  checkSat(lines, type, title, days) {
    new Dialog(DialogCheck, {
      baseEntity: this.baseEntity,
      childrenEntity: lines,
      childrenType: type,
      childrenTitle: title,
      days: Number(days),
    }).show();
  }
}
