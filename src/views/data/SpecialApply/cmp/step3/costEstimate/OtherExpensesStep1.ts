import { Vue, Component, Prop } from 'vue-property-decorator';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import SpecialApplyBillOtherExpenesApi from '@/http/price/controller/specialapply/otherexpenses/SpecialApplyBillOtherExpenesApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';

// MODELS
import SpecialApplyOtherExpensesMatLine from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyOtherExpensesMatLine';
import SpecialApplyMatLine from '@/model/remote/price/api/specialapply/mat/SpecialApplyMatLine';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyOtherExpensesMatLineSaveRequest from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyOtherExpensesMatLineSaveRequest';
import SpecialApplyCurrencyExchangeRate from '@/model/remote/price/api/specialapply/bill/SpecialApplyCurrencyExchangeRate';
import SpecialApplyInterTransPremiumRuleSaveRequest from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyInterTransPremiumRuleSaveRequest';
import SpecialApplyInterTransPremiumRule from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyInterTransPremiumRule';
// COMPONENTES
import Dialog from '@/components/dialog/Dialog';
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import DialogExtent from './DialogExtent.vue';
import ExchangeRateDialog from './ExchangeRateDialog.vue';
import SettingFee from './SettingFee.vue';

@Component({
  name: 'OtherExpensesStep1',
  components: { SearchRemote, NumberInput, ExchangeRateDialog },
})
export default class OtherExpensesStep1 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  height: string = '400px';
  entity: SpecialApplyOtherExpensesMatLine[] = [];
  currency: string = '';
  ObjectUtil = ObjectUtil;

  // 汇率相关
  exchangeRateDialogVisible: boolean = false;
  exchangeRateData: SpecialApplyCurrencyExchangeRate = new SpecialApplyCurrencyExchangeRate();

  // 国内运输保险费是否投保，默认投保
  domesticInsurancePremium: boolean = true;

  created() {
    this.getMatExpenes();
    this.getExchangeRateData();
  }

  // 获取其他费用数据
  getMatExpenes() {
    SpecialApplyBillOtherExpenesApi.getMatExpenes(this.baseEntity.id!)
      .then((res) => {
        let arr3: any = [];
        // 根据接口返回类型变更，现在返回的是SpecialApplyOtherExpensesMatLineSaveRequest
        const responseData = res.data;
        this.entity = responseData?.lines || [];

        // 同步国内运输保险费投保状态
        if (responseData?.domesticTransInsurancePremium !== undefined) {
          this.domesticInsurancePremium = responseData.domesticTransInsurancePremium;
        }

        this.entity.forEach((item) => {
          let arr: any = [];
          let arr1: any = [];
          let arr2: any = [];
          item.currencyIds = item.currencyIds || [];
          item.currencyIds.forEach((itx) => {
            arr3.push(itx.currencyId ? itx.currencyId : '');
          });
          item.currencyIds = item.currencyIds || [];
          item.currencyIds.forEach((itx, index) => {
            if (item.giftAccessoryAmount && item.giftAccessoryAmount.fees.length > 0) {
              arr.push({
                value: item.giftAccessoryAmount.fees[index]
                  ? item.giftAccessoryAmount.fees[index].value
                  : 0,
                currencyId: itx.currencyId,
                currencySymbol: itx.currencySymbol,
              });
            } else {
              arr.push({
                value: 0,
                currencyId: itx.currencyId,
                currencySymbol: itx.currencySymbol,
              });
            }
            if (item.commission && item.commission.fees.length > 0) {
              arr1.push({
                value: item.commission.fees[index] ? item.commission.fees[index].value : 0,
                currencyId: itx.currencyId,
                currencySymbol: itx.currencySymbol,
              });
            } else {
              arr1.push({
                value: 0,
                currencyId: itx.currencyId,
                currencySymbol: itx.currencySymbol,
              });
            }
            if (item.otherExpenses && item.otherExpenses.fees.length > 0) {
              arr2.push({
                value: item.otherExpenses.fees[index] ? item.otherExpenses.fees[index].value : 0,
                currencyId: itx.currencyId,
                currencySymbol: itx.currencySymbol,
              });
            } else {
              arr2.push({
                value: 0,
                currencyId: itx.currencyId,
                currencySymbol: itx.currencySymbol,
              });
            }
          });
          item.giftAccessoryAmount = {
            fees: JSON.parse(JSON.stringify(arr)),
          };
          item.commission = {
            fees: JSON.parse(JSON.stringify(arr1)),
          };
          item.otherExpenses = {
            fees: JSON.parse(JSON.stringify(arr2)),
          };
        });
        this.currency = Array.from(new Set(arr3)).join('、');
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }

  // 获取汇率数据
  getExchangeRateData() {
    SpecialApplyBillApi.listCurrencyExchangeRate(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.exchangeRateData = res.data;
        }
      })
      .catch((error) => {
        console.error('获取汇率数据失败:', error);
      });
  }

  // 显示汇率弹窗
  showExchangeRateDialog() {
    this.exchangeRateDialogVisible = true;
  }

  // 显示国际运输保险费率弹窗
  showInsuranceRateDialog() {
    // 先调用获取接口
    SpecialApplyBillOtherExpenesApi.getInterTransPremiumRule(this.baseEntity.id!)
      .then((res) => {
        const responseData = res.data;
        const rule = responseData?.rule;

        if (!rule) {
          return this.$message.error('未获取到国际运输保险费规则');
        }

        new Dialog(SettingFee, {
          transportTypeId: this.baseEntity.transportTypeId,
          entity: rule, // 直接传入后端返回的规则对象
          success: (selected) => {
            this.saveInterTransPremiumRule(selected);
          },
        }).show();
      })
      .catch((error) => {
        this.$message.error(error.message || '获取国际运输保险费规则失败');
      });
  }

  // 保存国际运输保险费率
  saveInterTransPremiumRule(formData) {
    const saveRequest = new SpecialApplyInterTransPremiumRuleSaveRequest();
    saveRequest.owner = this.baseEntity.id!;
    saveRequest.version = this.baseEntity.version!;

    const rule = new SpecialApplyInterTransPremiumRule();
    rule.advanceRate = formData.advanceRate;
    rule.type = formData.type;
    rule.owner = this.baseEntity.id!;

    saveRequest.rule = rule;

    SpecialApplyBillOtherExpenesApi.saveInterTransPremiumRule(saveRequest)
      .then(() => {
        // 保存成功后刷新页面数据，因为后端会重新计算费用并更新单据版本
        this.$emit('refresh');
      })
      .catch((error) => {
        this.$message.error(error.message || '保存国际运输保险费率失败');
      });
  }

  // 国内运输保险费投保状态变更
  domesticInsuranceChange(value: boolean) {
    console.log('国内运输保险费投保状态变更:', value);
    this.domesticInsurancePremium = value;
  }

  mounted() {
    this.resizeHandler();
    window.addEventListener('resize', this.resizeHandler);
  }
  // 添加/修改延保政策
  doAddExtended(row, mode, index) {
    new Dialog(DialogExtent, {
      mode,
      initSelected: row.scopeExtendeds || [],
      baseEntity: this.baseEntity,
      matCd: row.matLine.matCd,
      success: (tableData) => {
        this.entity[index].scopeExtendeds = tableData;
      },
    }).show();
  }
  // 删除延保政策
  doDelete(row) {
    row.extendedFee = null;
    row.extendedInfo = null;
  }
  // 校验
  async validate() {
    // CIF场景下校验国际运输保险费率是否已设置
    if (this.baseEntity.incotermsId === 'CIF') {
      try {
        const rateResult = await SpecialApplyBillOtherExpenesApi.getInterTransPremiumRule(
          this.baseEntity.id!
        );
        if (!rateResult.data?.rule?.advanceRate || !rateResult.data?.rule?.type) {
          throw new Error('请设置国际运输保险费率');
        }
      } catch (error) {
        throw new Error('请设置国际运输保险费率');
      }
    }
    return true;
  }

  // 保存
  async doSave() {
    // 先进行校验
    await this.validate();

    let params: SpecialApplyOtherExpensesMatLineSaveRequest = {
      owner: this.baseEntity.id as string,
      check: true,
      lines: this.entity,
      version: this.baseEntity.version as number,
      domesticTransInsurancePremium: this.domesticInsurancePremium,
    };
    return SpecialApplyBillOtherExpenesApi.saveMatExpenes(params);
  }
  resizeHandler() {
    this.height = window.innerHeight - 262 + 'px';
  }
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler);
  }
}
