<template>
  <div class="tab-step1" ref="step1" :style="{ '--height': height }">
    <div style="margin-bottom: 5px">以下费用均默认以【提交价格申请当天日期】计算得出。</div>
    <!-- 币种信息显示 -->
    <div class="currency-info">
      <div class="currency-left">
        <span class="label">币种：</span>
        <span class="value">{{ currency }}</span>
        <el-button type="text" @click="showExchangeRateDialog" class="exchange-rate-btn">
          查看汇率
        </el-button>
      </div>
      <div class="currency-right">
        <!-- 供货场景：国内运输保险费是否投保 -->
        <div v-if="baseEntity.exportTypeId === 'supply'" class="domestic-insurance">
          <span class="label">国内运输保险费是否投保：</span>
          <el-switch
            v-model="domesticInsurancePremium"
            size="mini"
            @change="domesticInsuranceChange"
            :disabled="true"
          ></el-switch>
        </div>
      </div>
    </div>
    <div class="tab-step1-table">
      <el-table :data="entity">
        <el-table-column type="index" label="行号" width="50" fixed="left"> </el-table-column>
        <el-table-column type="index" label="机型" width="100" fixed="left">
          <template #default="scoped">
            {{ scoped.row.specialApplyMatLine ? scoped.row.specialApplyMatLine.prodMdlCode : '--' }}
          </template>
        </el-table-column>
        <el-table-column label="整机物料号" prop="matCd" minWidth="150" fixed="left">
          <template #default="scoped">
            {{ scoped.row.specialApplyMatLine ? scoped.row.specialApplyMatLine.matCd : '--' }}
          </template>
        </el-table-column>
        <el-table-column label="台量" prop="qty" minWidth="50">
          <template #default="scoped">
            {{
              scoped.row.specialApplyMatLine.limitQty ? scoped.row.specialApplyMatLine.qty : '不限'
            }}
            <!-- {{ scoped.row.matLine.limitQty ? scoped.row.matLine.qty : '不限' }} -->
          </template>
        </el-table-column>
        <!-- 单台费用分组 -->
        <el-table-column label="单台费用" align="center">
          <!-- 国内运输保险费/台 -->
          <el-table-column prop="domesticTransportationInsurance" minWidth="176">
            <template #header>
              <span>国内运输保险费</span>
            </template>
            <template #default="scoped">
              <el-button
                type="text"
                @click="
                  checkSat(
                    scoped.row.domesticTransportationInsurance,
                    'domesticTransportationInsurance',
                    '国内运输保险费'
                  )
                "
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column minWidth="140">
            <template #header>
              <span>延长保险费</span>
              <el-tooltip content="添加延保政策收取的费用" placement="top-start">
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <el-button
                type="text"
                @click="checkSat(scoped.row.extendedInfos, 'extendedInfos', '延保费')"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="commission" minWidth="100">
            <template #header>
              <span>客户佣金</span>
              <el-tooltip content="本单需要收取的客户佣金" placement="top-start">
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <div
                v-for="(item, index) in scoped.row.commission"
                :key="index"
                style="display: flex"
              >
                <div>
                  {{ item.currencySymbol }}
                </div>
                <div>
                  {{ item.value | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="giftAccessoryAmount" minWidth="130">
            <template #header>
              <span>赠送配件金额</span>
              <el-tooltip content="本单需要收取的赠送配件金额" placement="top-start">
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <div
                v-for="(item, index) in scoped.row.giftAccessoryAmount"
                :key="index"
                style="display: flex"
              >
                <div>
                  {{ item.currencySymbol }}
                </div>
                <div>
                  {{ item.value | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="other" minWidth="100">
            <template #header>
              <span>额外费用</span>
              <el-tooltip content="本单需要收取的额外费用" placement="top-start">
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <div v-for="(item, index) in scoped.row.other" :key="index" style="display: flex">
                <div>
                  {{ item.currencySymbol }}
                </div>
                <div>
                  {{ item.value | numberFilter('--', 0, true) }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column minWidth="176">
            <template #header>
              <span>远期贴现费</span>
              <el-tooltip
                content="远期贴现费=(设备报价+客户佣金+赠送配件金额+额外费用+延长保修费)*X%(远期比例)*远期天数*远期贴现费年利率/360"
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <el-button
                type="text"
                @click="checkSat(scoped.row.forwardDiscount, 'forwardDiscount', '远期贴现费')"
              >
                查看
              </el-button>
            </template>
          </el-table-column>
          <el-table-column minWidth="176">
            <template #header>
              <span>信保费</span>
              <el-tooltip
                content="信保费=(设备报价+客户佣金+赠送配件金额+额外费用+延长保修费+远期贴现费)*X%(远期比例)*信保费率"
                placement="top-start"
              >
                <i class="el-icon-info icon-info"></i>
              </el-tooltip>
            </template>
            <template #default="scoped">
              <el-button type="text" @click="checkSat(scoped.row.premium, 'premium', '信保费')">
                查看
              </el-button>
            </template>
          </el-table-column>
          <!-- CIF场景：国际运输保险费/台 -->
          <el-table-column
            v-if="baseEntity.incotermsId === 'CIF'"
            prop="internationalInsurance"
            minWidth="176"
          >
            <template #header>
              <span>国际运输保险费</span>
            </template>
            <template #default="scoped">
              <el-button
                type="text"
                @click="
                  checkSat(
                    scoped.row.internationalInsurance,
                    'internationalInsurance',
                    '国际运输保险费'
                  )
                "
              >
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <CostEstimateDialogCheck
      v-model="showDialogCheck"
      :childrenEntity="childrenEntity"
      :baseEntity="baseEntity"
      :childrenType="childrenType"
      :childrenTitle="childrenTitle"
    ></CostEstimateDialogCheck>

    <!-- 汇率弹窗组件 -->
    <ExchangeRateDialog
      :visible.sync="exchangeRateDialogVisible"
      :exchange-rate-data="exchangeRateData"
      @close="exchangeRateDialogVisible = false"
    />
  </div>
</template>

<script src="./OtherExpensesStep3.ts"></script>

<style lang="scss" scoped>
.tab-step1 {
  min-height: 282px;
  height: var(--height);
  overflow-y: auto;

  .currency-info {
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .currency-left {
      display: flex;
      align-items: center;

      .label {
        margin-right: 8px;
      }

      .value {
        margin-right: 8px;
      }

      .exchange-rate-btn {
        padding: 0;
        font-size: 14px;
        color: #409eff;

        &:hover {
          color: #66b1ff;
        }
      }
    }

    .currency-right {
      .domestic-insurance {
        display: flex;
        align-items: center;

        .label {
          margin-right: 8px;
          font-size: 14px;
          color: #606266;
        }
      }
    }
  }

  &-table {
    .cell-text {
      color: #3b71fc;
      font-size: 12px;
      cursor: pointer;
    }
    .cell-btn {
      color: #79879e;
      font-size: 12px;
      margin-left: 4px;
      cursor: pointer;
    }
    .icon-info {
      margin-top: 4px;
      color: #a1b0c8;
      font-size: 14px;
      margin: 0px 4px;
      &:hover {
        color: $--color-primary;
      }
    }
  }
}
/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 0px;
  padding-bottom: 0px;
}
</style>
