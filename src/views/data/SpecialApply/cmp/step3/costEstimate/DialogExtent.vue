<!--
 * @Author: 张文轩
 * @Date: 2023-12-22 14:44:25
 * @LastEditTime: 2024-08-23 15:50:31
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step4\SelectExtended.vue
 * 记得注释
-->
<template>
  <el-dialog title="延保政策" width="60%" @close="handleCancel" :visible="true">
    <el-table
      ref="table"
      row-key="policyId"
      :data="tableData"
      max-height="450px"
      v-loading="loading"
    >
      <el-table-column v-if="mode === 'edit'" type="selection" width="50" :reserve-selection="true">
      </el-table-column>
      <el-table-column label="子公司" minWidth="100">
        <template #default="scoped">
          <div>{{ scoped.row.applicationScope.sbsdyName }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="办事处"
        prop="matDesc"
        minWidth="100"
        v-if="baseEntity.specialType != 'adjustZoneGuidePrice'"
      >
        <template #default="scoped">
          <div>{{ scoped.row.applicationScope.ofcName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="销往国" minWidth="100">
        <template #default="scoped">
          {{ formatCtryName(scoped.row.applicationScope) }}
        </template>
      </el-table-column>
      <el-table-column
        label="客户"
        minWidth="100"
        v-if="baseEntity.specialType == 'importantProjects'"
      >
        <template #default="scoped">
          <div>{{ scoped.row.applicationScope.custName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="币种" minWidth="100">
        <template #default="scoped">
          {{ scoped.row.applicationScope.currencyId }}
        </template>
      </el-table-column>
      <el-table-column label="延保费/台" prop="endHourReading" minWidth="120">
        <template #default="scoped">
          <div v-if="scoped.row.total" style="display: flex">
            <div>{{ scoped.row.applicationScope.currencySymbol }}</div>
            <div>{{ Math.ceil(scoped.row.total) }}</div>
          </div>
          <div v-else style="display: flex">
            {{ scoped.row.applicationScope.currencySymbol }}
            <div>0</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="延保政策" prop="endHourReading" minWidth="120">
        <template #default="scoped">
          <!-- 已选择延保政策 -->
          <div>
            <el-button
              type="text"
              v-if="scoped.row.extendedInfos && scoped.row.extendedInfos.length > 0"
              @click="doAddExtended(scoped.row, 'view', scoped.$index)"
              >查看</el-button
            >
            <el-button
              type="text"
              v-if="scoped.row.extendedInfos && scoped.row.extendedInfos.length > 0"
              @click="doAddExtended(scoped.row, 'edit', scoped.$index, 'titleEdit')"
              >编辑</el-button
            >
            <el-button
              type="text"
              v-if="!scoped.row.extendedInfos || scoped.row.extendedInfos.length <= 0"
              @click="doAddExtended(scoped.row, 'edit', scoped.$index, 'titleAdd')"
              >添加</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" @click="handleCancel">取消</el-button>
      <el-button size="mini" type="primary" @click="handleSubmit"> 确定 </el-button>
    </div>
  </el-dialog>
</template>

<script src="./DialogExtent.ts"></script>

<style lang="scss" scoped>
/deep/ .el-dialog__header {
  padding: 10px 20px;
  font-size: 14px;
  color: #242633;
  line-height: 22px;
  .el-dialog__headerbtn {
    top: 10px;
  }
}
/deep/ .el-dialog__body {
  padding-top: 16px;
}
/deep/ .el-table__cell {
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .cell {
  padding: 0px 12px;
}
.el-form-item {
  margin-bottom: 12px;
}
</style>
