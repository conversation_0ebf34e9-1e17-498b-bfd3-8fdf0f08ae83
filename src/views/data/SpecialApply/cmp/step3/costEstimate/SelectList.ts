import { Vue, Component, Prop } from 'vue-property-decorator';
import QuotationApplyBillApi from '@/http/price/controller/quotationapply/bill/QuotationApplyBillApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
import CommonInfoApi from '@/http/data/CommonInfoApi';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import { QuotationApplyState } from '@/model/remote/price/api/quotationapply/bill/QuotationApplyState';
import FormItem from '@/components/form/form-item/index.vue';
import TableSingleRadio from '@/components/dialog/TableSingleRadio.vue';
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import StatePopover from '@/components/state-popover/StatePopover.vue';
import ApproveNodeApi from '@/http/price/controller/approvenode/ApproveNodeApi';

@Component({
  name: 'SelectList',
  components: { FormItem, TableSingleRadio, SearchRemote, StatePopover },
})
export default class SelectList extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  @Prop({ type: Function }) success;
  $refs: any;
  loading = false;
  filter: any = this.defaultFilter();
  paging: any = {
    pageNum: 1,
    pageSize: 20,
  };
  tableData: SpecialApplyBill[] = [];
  total: number = 0;
  singleSelected = null; //单选选中项
  stateList: any[] = [
    { label: '全部', value: '' },
    { label: '草拟', value: QuotationApplyState.draft },
    { label: '审批中', value: QuotationApplyState.approving },
    { label: '审批通过', value: QuotationApplyState.approved },
    { label: '已驳回', value: QuotationApplyState.reject },
    { label: '已取消', value: QuotationApplyState.canceled },
    { label: '已作废', value: QuotationApplyState.abort },
  ]; // 状态
  approveNodeIdList: any[] = []; // 审批节点

  defaultFilter() {
    return {
      topicLike: '', // 主题 类似于
      idStart: '', // 单号 起始于
      custIdEq: '', // 客户ID 等于
      stateEq: '', // 状态 等于
      processNodeNameEq: '全部', // 审批节点 等于
      referContractEq: '', // 关联合同 等于
      sbsdyIdEq: '', // 子公司ID 等于
      ofcIdEq: '', // 办事处ID 等于
    };
  }

  doReset() {
    this.filter = this.defaultFilter();
    this.doSearch();
  }

  created() {
    this.getContractTypeList();
    this.doSearch();
  }

  // 获取审批节点列表
  getContractTypeList() {
    ApproveNodeApi.listDefinitions('ltc_special_apply_bill')
      .then((res) => {
        if (res.data) {
          this.approveNodeIdList = [{ name: '全部', code: '' }, ...res.data];
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }
  checkSingleSelectable() {
    return true;
  }
  doSearch() {
    this.loading = true;
    SpecialApplyBillApi.query(this.buildFilter())
      .then((res) => {
        this.tableData = res.data || [];
        this.total = res.total!;
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  doPageChange(page) {
    this.paging.pageNum = page;
    this.doSearch();
  }
  doSizeChange(size) {
    this.paging.pageSize = size;
    this.doSearch();
  }
  // 构造查询条件
  buildFilter() {
    let paramIn: any = {};
    for (let key in this.filter) {
      let value = this.filter[key];
      if (value == null || value === '' || value == undefined) {
        continue;
      }
      paramIn[key] = value;
    }
    paramIn.pageNum = this.paging.pageNum;
    paramIn.pageSize = this.paging.pageSize;
    paramIn.specialTypeEq = this.baseEntity.specialType;
    if (paramIn.processNodeNameEq === '全部') {
      delete paramIn.processNodeNameEq;
    }
    return paramIn;
  }
  // 查询历史任务
  queryHistoryTask(row) {
    return QuotationApplyBillApi.listHistoryTask(row.id);
  }
  // 点击行
  rowClick(row, column, event) {
    this.singleSelected = row;
  }
  handleCancel() {
    this.closeWindow();
  }
  handleSubmit() {
    if (!this.singleSelected) {
      return this.$message.error('请选择一条数据');
    }
    this.success(this.singleSelected);
    this.handleCancel();
  }
  // 客户搜索
  queryCustomerList(query, page) {
    let paramIn = {
      custNmOrNum: query,
    };
    const par = Object.assign(paramIn, { pageNum: page, pageSize: 20 });
    return CommonInfoApi.queryCustomerList(par);
  }
  customValueFormat(item) {
    return item.custId;
  }
  cusomeLabelFormat(item) {
    if (this.$i18n.locale == 'en') {
      return '[' + item.custNum + ']' + item.custNmEn;
    } else {
      return '[' + item.custNum + ']' + item.custNm;
    }
  }
  // 子公司查询
  querySideCompanyList(query, page) {
    let paramIn = {
      sbsdyNm: query,
    };
    const par = Object.assign(paramIn, { pageNum: page, pageSize: 20 });
    return CommonInfoApi.subsidiaryList(par);
  }
  sbsyValueFormat(item) {
    return item.sbsdyId;
  }
  sbsyLabelFormat(item) {
    return item.sbsdyNm;
  }
  // 办事处搜索
  queryOfficeList(query, page) {
    let paramIn = {
      lang: this.$i18n.locale,
      officeIdEquals: null,
      officeNameLike: query,
    };
    const par = Object.assign(paramIn, { pageNum: page, pageSize: 20 });
    return CommonInfoApi.officeList(par);
  }
  officeValueFormat(item) {
    return item.officeId;
  }
  officeLabelFormat(item) {
    return item.officeName;
  }

  // 销往国查询
  queryCountryList(query, page) {
    let paramIn = {
      ctryNm: query,
    };
    const par = Object.assign(paramIn, { pageNum: page, pageSize: 20 });
    return CommonInfoApi.countryList(par);
  }
  countryValueFormat(item) {
    return item.ctryIdStr;
  }
  countryLabelFormat(item) {
    return item.ctryNm;
  }
}
