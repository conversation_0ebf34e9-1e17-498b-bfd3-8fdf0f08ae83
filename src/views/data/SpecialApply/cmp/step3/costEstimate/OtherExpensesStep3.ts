import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
// HTTPS
import SpecialApplyBillOtherExpenesApi from '@/http/price/controller/specialapply/otherexpenses/SpecialApplyBillOtherExpenesApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';

// MODELS
import SpecialApplyMatLine from '@/model/remote/price/api/specialapply/mat/SpecialApplyMatLine';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyOtherExpensesMatLineSaveRequest from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyOtherExpensesMatLineSaveRequest';
import SpecialApplyOtherExpensesSummary from '@/model/remote/price/api/specialapply/bill/SpecialApplyOtherExpensesSummary';
import ApplicationScopeExpenseRate from '@/model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExpenseRate';
import SpecialApplyCurrencyExchangeRate from '@/model/remote/price/api/specialapply/bill/SpecialApplyCurrencyExchangeRate';

// COMPONENTES
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import NumberInput from '@/components/form/number-input/NumberInput.vue';
import CostEstimateDialogCheck from './CostEstimateDialogCheck.vue';
import ExchangeRateDialog from './ExchangeRateDialog.vue';

@Component({
  name: 'OtherExpensesStep3',
  components: { SearchRemote, NumberInput, CostEstimateDialogCheck, ExchangeRateDialog },
})
export default class OtherExpensesStep3 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  tableData: SpecialApplyMatLine[] = [];
  height: string = '400px';
  entity: SpecialApplyOtherExpensesSummary[] = [];
  currency: string = '';
  showDialogCheck: boolean = false;
  childrenEntity: ApplicationScopeExpenseRate[] = [];
  childrenType: string = '';
  childrenTitle: string = '';
  @Prop({ type: Boolean, default: true }) isHeight;

  // 国内运输保险费是否投保，默认投保
  // 注意：这是Step3的只读展示，实际的投保状态应该从后端获取
  domesticInsurancePremium: boolean = true;

  // 汇率相关
  exchangeRateDialogVisible: boolean = false;
  exchangeRateData: SpecialApplyCurrencyExchangeRate = new SpecialApplyCurrencyExchangeRate();

  created() {
    this.getExpensesSummary();
    this.getExchangeRateData();
  }

  // 获取其他费用汇总数据
  getExpensesSummary() {
    SpecialApplyBillOtherExpenesApi.getExpensesSummary(this.baseEntity.id!)
      .then((res) => {
        this.entity = res.data?.summaryList || [];
        let arr3: any = [];
        this.entity.forEach((item) => {
          if (item.other && Array.isArray(item.other)) {
            item.other.forEach((itx) => {
              arr3.push(itx.currencyId);
            });
          }
        });
        this.currency = Array.from(new Set(arr3)).join('、');
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      });
  }

  // 获取汇率数据
  getExchangeRateData() {
    SpecialApplyBillApi.listCurrencyExchangeRate(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.exchangeRateData = res.data;
        }
      })
      .catch((error) => {
        console.error('获取汇率数据失败:', error);
      });
  }
  mounted() {
    this.resizeHandler();
    window.addEventListener('resize', this.resizeHandler);
  }
  // 删除延保政策
  doDelete(row) {
    row.extendedFee = null;
    row.extendedInfo = null;
  }
  // // 保存
  // doSave() {
  //   let params: SpecialApplyOtherExpensesMatLineSaveRequest = {
  //     owner: this.baseEntity.id,
  //     check: true,
  //     lines: this.entity,
  //   };
  //   return SpecialApplyBillOtherExpenesApi.saveMatExpenes(params);
  // }
  resizeHandler() {
    if (this.isHeight) {
      this.height = window.innerHeight - 262 + 'px';
    } else {
      // this.height =
    }
  }
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler);
  }

  // 显示汇率弹窗
  showExchangeRateDialog() {
    this.exchangeRateDialogVisible = true;
  }

  // 国内运输保险费投保状态变更
  domesticInsuranceChange(value) {
    // 只读，不处理
    return;
  }

  checkSat(row, type, title) {
    // 检查row是否为空或不是数组
    if (!row || !Array.isArray(row)) {
      console.log('暂无数据');
      this.$message.error('暂无数据');
      return;
    }

    let arr = row.map((item) => {
      return {
        total: item.total,
        extendedInfos: item.extendedInfos,
        applicationScop: item.applicationScope,
      };
    });
    this.showDialogCheck = true;
    this.childrenEntity = type == 'extendedInfos' ? arr : row;
    this.childrenType = type;
    this.childrenTitle = title;
  }

  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }
}
