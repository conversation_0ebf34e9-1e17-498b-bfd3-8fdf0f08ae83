/*
 * @Author: 张文轩
 * @Date: 2024-07-23 10:32:45
 * @LastEditTime: 2024-08-23 15:53:54
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step4\SelectExtended.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
// HTTPS
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';

// COMPONENTES
import FormItem from '@/components/form/form-item/index.vue';
import TableSingleRadio from '@/components/dialog/TableSingleRadio.vue';

@Component({
  name: 'CostEstimateDialogCheck',
  components: { FormItem, TableSingleRadio },
})
export default class CostEstimateDialogCheck extends Vue {
  @Prop({ type: Array, default: () => [] }) childrenEntity: any[];
  @Prop({ type: String, default: '' }) childrenType: string;
  @Prop({ type: String, default: '' }) childrenTitle: string;
  @Prop({ type: Number, default: 0 }) days: number;
  @Prop({ type: Object, default: () => new SpecialApplyBill() }) baseEntity: SpecialApplyBill;
  @Prop({ type: Boolean, default: false }) value: boolean;
  $refs: any;
  get localValue() {
    return this.value;
  }
  set localValue(val) {
    if (!val) {
      // 当设置为false时，关闭弹窗
      this.$emit('hide');
    }
    this.$emit('input', val);
  }

  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }
}
