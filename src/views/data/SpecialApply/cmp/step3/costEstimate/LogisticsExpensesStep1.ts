import { Vue, Component, Prop } from 'vue-property-decorator';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyLogisticsExpensesMatInfo from '@/model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesMatInfo';
import SpecialApplyLogisticsExpensesMatLine from '@/model/remote/price/api/specialapply/logisticsexpenses/SpecialApplyLogisticsExpensesMatLine';

import SpecialApplyFee from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyFee';
import SpecialApplyCurrency from '@/model/remote/price/api/specialapply/otherexpenses/SpecialApplyCurrency';
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType';
import DomesticFreightPortApi from '@/http/data/DomesticFreightPortApi';
import DomesticFreightLandApi from '@/http/data/DomesticFreightLandApi';
import SpecialApplyBillLogisticsExpensesApi from '@/http/price/controller/specialapply/logisticsexpenses/SpecialApplyBillLogisticsExpensesApi';
import SearchRemote from '@/components/form/search-remote/SearchRemote.vue';
import ThousNumber from '@/components/form/thous-number/ThousNumber.vue';

@Component({
  name: 'LogisticsExpensesStep1',
  components: { SearchRemote, ThousNumber },
})
export default class LogisticsExpensesStep1 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill;

  @Prop({ type: Object, default: new SpecialApplyLogisticsExpensesMatInfo() })
  entity: SpecialApplyLogisticsExpensesMatInfo;

  $refs: any;

  get tableData() {
    return this.entity.lines || [];
  }

  set tableData(value: SpecialApplyLogisticsExpensesMatLine[]) {
    this.entity.lines = value;
  }

  // 统一设置弹窗是否展示
  popoverVisible: boolean = false;
  // 统一设置选中值
  defaultFreightPortSelect: string = '';
  // 统一设置选中的完整对象
  defaultFreightPortObject: any = null;

  // 是否为自营模式
  get isSelfMode() {
    return this.baseEntity.exportTypeId === ExportType.self;
  }

  // 是否为供货模式
  get isSupplyMode() {
    return this.baseEntity.exportTypeId === ExportType.supply;
  }

  mounted() {
    this.$emit('refresh');
    // 表格默认滚动到最右侧，优先展示需要用户填写的列
    this.$nextTick(() => {
      this.scrollTableToRight();
    });
  }

  // 处理统一设置选择变化
  handleDefaultPortSelectChange(selectedPort: any) {
    this.defaultFreightPortObject = selectedPort;
  }

  // 统一设置默认起运港/起运站处理
  handleDefaultFreightPortChange() {
    this.popoverVisible = false;
    let code: string | null = null;
    let name: string | null = null;
    if (this.defaultFreightPortObject) {
      code = this.defaultFreightPortObject.code;
      name = this.defaultFreightPortObject.name;
    }
    // 更新所有行的起运港
    this.entity.lines.forEach((line) => {
      line.domesticFreightPortCode = code;
      line.domesticFreightPortName = name;
    });
  }

  // 表格滚动到最右侧
  scrollTableToRight() {
    const tableWrapper = this.$el.querySelector('.el-table__body-wrapper');
    if (tableWrapper) {
      tableWrapper.scrollLeft = tableWrapper.scrollWidth;
    }
  }

  // 检查是否有数据已填写
  hasDataFilled() {
    return this.tableData.some(
      (row) =>
        row.domesticFreightLandCode ||
        row.domesticFreightPortCode ||
        (row.packingFees && row.packingFees.some((fee) => (fee.value || 0) > 0))
    );
  }

  // 清空数据
  clearData() {
    // 清空表格数据
    this.tableData.forEach((row) => {
      row.domesticFreightLandCode = null;
      row.domesticFreightLandName = null;
      row.domesticFreightPortCode = null;
      row.domesticFreightPortName = null;
      if (row.packingFees) {
        row.packingFees.forEach((fee) => {
          fee.value = 0;
        });
      }
    });

    // 清空组件自身的数据
    this.popoverVisible = false;
    this.defaultFreightPortSelect = '';
    this.defaultFreightPortObject = null;
  }

  // 获取装箱费
  getPackingFee(row: SpecialApplyLogisticsExpensesMatLine, currency: SpecialApplyCurrency) {
    const fee = row.packingFees?.find((f) => f.currencyId === currency.currencyId);
    const value = fee?.value;
    return typeof value === 'string' ? Number(value) : value;
  }

  // 设置装箱费
  setPackingFee(
    row: SpecialApplyLogisticsExpensesMatLine,
    currency: SpecialApplyCurrency,
    amount: number
  ) {
    if (!row.packingFees) {
      row.packingFees = [];
    }
    let fee = row.packingFees.find((f) => f.currencyId === currency.currencyId);
    if (!fee) {
      fee = new SpecialApplyFee();
      fee.currencyId = currency.currencyId;
      fee.currencySymbol = currency.currencySymbol;
      row.packingFees.push(fee);
    }
    fee.value = amount;
  }

  // 查询起运港/起运站
  queryFreightPort(query: string, page: number) {
    let body = {
      province: null,
      code: null,
      name: query,
      enabled: 1, // 查询启用状态
      pageNum: page,
      pageSize: 20,
    };
    return DomesticFreightPortApi.list(body);
  }

  // 起运港/起运站值格式化
  freightPortValueFormat(item: any) {
    return item.code;
  }

  // 起运港/起运站标签格式化
  freightPortLabelFormat(item: any) {
    return item.name;
  }

  // 查询发货地/出发地
  queryFreightLand(query: string, page: number) {
    let body = {
      code: null,
      name: query,
      enabled: 1, // 查询启用状态
      note: null,
      pageNum: page,
      pageSize: 20,
    };
    return DomesticFreightLandApi.list(body);
  }

  // 发货地/出发地值格式化
  freightLandValueFormat(item: any) {
    return item.code;
  }

  // 发货地/出发地标签格式化
  freightLandLabelFormat(item: any) {
    return item.name;
  }

  // 起运港/起运站变化处理
  handleFreightPortChange(value: any, row: any) {
    if (value) {
      row.domesticFreightPortName = value.name;
    } else {
      row.domesticFreightPortName = null;
    }
  }

  // 发货地/出发地变化处理
  handleFreightLandChange(value: any, row: any) {
    if (value) {
      row.domesticFreightLandName = value.name;
    } else {
      row.domesticFreightLandName = null;
    }
  }

  // 保存数据
  async doSave() {
    return new Promise((resolve, reject) => {
      // 校验表单
      // @ts-ignore
      this.$refs.form.validate((valid: boolean) => {
        if (!valid) {
          reject(new Error('存在必填项未填写，请填写后再保存'));
          return;
        }
        // 调用API保存数据
        SpecialApplyBillLogisticsExpensesApi.saveMatInfo(this.entity)
          .then((res: any) => {
            this.$emit('refresh');
            resolve(true);
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    });
  }
}
