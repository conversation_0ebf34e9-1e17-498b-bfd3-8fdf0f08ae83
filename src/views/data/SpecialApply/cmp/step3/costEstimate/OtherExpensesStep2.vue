<template>
  <div class="tab-step2" ref="step2" :style="{ '--height': height }">
    <div class="tab-step2-table">
      <el-form size="mini" ref="form" :model="form" label-width="0px" :rules="rules">
        <el-table ref="table" :data="form.lines" v-loading="loading">
          <el-table-column label="付款方式" minWidth="200">
            <template #default="scoped">
              <el-form-item
                :prop="'lines.' + scoped.$index + '.paymentTypeId'"
                :rules="ruleForm.paymentTypeId"
              >
                <el-cascader
                  size="mini"
                  v-model="scoped.row.paymentTypeId"
                  :ref="'cascader' + scoped.$index"
                  :show-all-levels="false"
                  :options="paymentTypeList"
                  :props="cascaderProps"
                  style="min-width: 200px; width: 70%"
                  @change="
                    () => {
                      paymentTypeChange(scoped.row, scoped.$index);
                    }
                  "
                ></el-cascader>
              </el-form-item>
              <div style="position: absolute; bottom: 0px">
                {{ scoped.row.paymentTypeName ? scoped.row.paymentTypeName : '--' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="付款比例" minWidth="104">
            <template #default="scoped">
              <el-form-item
                :prop="'lines.' + scoped.$index + '.percentage'"
                :rules="ruleForm.percentage"
              >
                <number-input
                  size="mini"
                  v-model="scoped.row.percentage"
                  xType="float"
                  :min="0.01"
                  :max="getRatioMax(scoped.$index)"
                >
                  <template slot="append">
                    <div style="width: 26px; height: 26px; line-height: 26px">%</div>
                  </template>
                </number-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="天数" minWidth="80">
            <template #default="scoped">
              <el-form-item>
                <number-input
                  v-if="scoped.row.allowedForward"
                  size="mini"
                  v-model="scoped.row.days"
                  xType="num"
                  @change="daysChange(scoped.row, scoped.$index)"
                >
                </number-input>
                <div v-else>--</div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="远期贴现费率" minWidth="104">
            <template #default="scoped">
              <el-form-item>
                <div v-if="scoped.row.allowedForward">
                  <div>
                    <el-button
                      type="text"
                      @click="
                        checkSat(
                          scoped.row.applicationScopeExpenseRate,
                          'occForwardDiscountRate',
                          '远期贴现费率',
                          scoped.row.days
                        )
                      "
                    >
                      查看
                    </el-button>
                  </div>
                </div>
                <div v-else>--</div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="信保费率" minWidth="104">
            <template #default="scoped">
              <el-form-item>
                <div v-if="scoped.row.allowedForward">
                  <div>
                    <el-button
                      type="text"
                      @click="
                        checkSat(
                          scoped.row.applicationScopeExpenseRate,
                          'occPremiumRate',
                          '信保费率',
                          scoped.row.days
                        )
                      "
                    >
                      查看
                    </el-button>
                  </div>
                </div>
                <div v-else>--</div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="操作" minWidth="80">
            <template #default="scoped">
              <el-form-item>
                <el-button type="text" @click="doDelete(scoped.$index)">删除</el-button>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <el-button
        v-if="showAddBtn"
        class="table-btn"
        icon="el-icon-plus"
        type="primary"
        size="mini"
        @click="doAdd"
      >
        添加
      </el-button>
    </div>
  </div>
</template>

<script src="./OtherExpensesStep2.ts"></script>

<style lang="scss" scoped>
.tab-step2 {
  min-height: 282px;
  height: var(--height);
  overflow-y: auto;
  &-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    &-text {
      font-size: 18px;
      font-weight: 600;
    }
  }
  &-table {
    height: calc(100% - 40px);
    .table-btn {
      color: #3b71fc;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #3b71fc;
      margin-top: 12px;
    }
  }
}
.el-radio {
  margin-right: 8px;
}
/deep/ .el-table__header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__fixed-header-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 6px;
  padding-bottom: 8px;
}
/deep/ .el-table__body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 16px;
  padding-bottom: 16px;
}
/deep/ .el-table__fixed-body-wrapper .el-table__cell {
  // 以下为UI样式
  padding-top: 16px;
  padding-bottom: 16px;
}
</style>
