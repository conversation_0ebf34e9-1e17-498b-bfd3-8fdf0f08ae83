/*
 * @Author: 张文轩
 * @Date: 2024-07-23 10:32:45
 * @LastEditTime: 2024-08-23 15:53:54
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step4\SelectExtended.ts
 * 记得注释
 */
import { Vue, Component, Prop } from 'vue-property-decorator';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
// MODELS
import ApplicationScopeExtended from '@/model/remote/price/api/specialapply/otherexpenses/ApplicationScopeExtended';
import BQuotationApplyBill from '@/model/remote/price/controller/quotationapply/bill/BQuotationApplyBill';
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';

// COMPONENTES
import Dialog from '@/components/dialog/Dialog';
import FormItem from '@/components/form/form-item/index.vue';
import TableSingleRadio from '@/components/dialog/TableSingleRadio.vue';
import SelectExtended from '@/views/data/ContractQuotation/cmp/step4/SelectExtended.vue';

@Component({
  name: 'DialogExtent',
  components: { FormItem, TableSingleRadio },
})
export default class DialogExtent extends Vue {
  mode: string = 'edit'; // 模式 edit: 编辑，view: 查看
  baseEntity: SpecialApplyBill = new SpecialApplyBill(); // 基础数据
  success; // 成功回调
  $refs: any;
  form: any = {
    type: '', // 赔付类型 F: 表示整机， K： 表示关键部件
    policyNameLike: '', // 政策名称
  };
  tableData: ApplicationScopeExtended[] = [];
  initSelected: ApplicationScopeExtended[] = []; // 初始化选中数据
  matCd: string = '';
  loading: boolean = false;
  ObjectUtil = ObjectUtil;
  typeList: any[] = [
    { value: 'F', label: '整机保修' },
    { value: 'P', label: '关键部件保修' },
  ];
  created() {
    // 初始化赋值
    if (this.mode === 'view') {
      this.tableData = JSON.parse(JSON.stringify(this.initSelected));
    }
  }

  typeFormatter(value) {
    if (!ObjectUtil.isNullOrBlank(value)) {
      if (value === 'F') {
        return '整机保修';
      }
      if (value === 'P') {
        return '关键部件保修';
      }
    }
    return '--';
  }
  handleCancel() {
    this.$emit('hide');
  }
  handleSubmit() {
    // 只能选择一个整机保修
    this.success(this.tableData);
    this.handleCancel();
  }
  // 添加/修改延保政策
  doAddExtended(row, mode, index, titleType) {
    new Dialog(SelectExtended, {
      mode,
      matCd: this.matCd,
      baseEntity: row.applicationScope,
      initSelected: row.extendedInfos || [],
      titleType: titleType,
      success: (selected) => {
        let num: number = 0;
        selected.forEach((item) => {
          num += item.price;
        });
        this.tableData[index].extendedInfos = selected || [];
        this.tableData[index].total = num * row.applicationScope.exchangeRate;
      },
    }).show();
  }
  doDelete() {}

  // 格式化销往国
  formatCtryName(applicationScope) {
    if (!applicationScope.ctrys || applicationScope.ctrys.length <= 0) {
      return '--';
    }
    let res: string[] = [];
    for (let ctry of applicationScope.ctrys) {
      for (let applicationScopeCtryItem of ctry.applicationScopeCtry) {
        res.push(applicationScopeCtryItem.name as string);
      }
    }
    if (res.length <= 0) {
      return '--';
    }
    return res.join('、');
  }
}
