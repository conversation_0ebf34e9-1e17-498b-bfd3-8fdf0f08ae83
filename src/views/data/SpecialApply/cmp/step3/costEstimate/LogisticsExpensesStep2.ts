import { Vue, Component, Prop } from 'vue-property-decorator';
// HTTPS
import SpecialApplyBillLogisticsExpensesApi from '@/http/price/controller/specialapply/logisticsexpenses/SpecialApplyBillLogisticsExpensesApi';
import SpecialApplyBillApi from '@/http/price/controller/specialapply/bill/SpecialApplyBillApi';
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import SpecialApplyLogisticsExpensesSummary from '@/model/remote/price/api/specialapply/bill/SpecialApplyLogisticsExpensesSummary';
import SpecialApplyCurrencyExchangeRate from '@/model/remote/price/api/specialapply/bill/SpecialApplyCurrencyExchangeRate';
// COMPONENTS
import ExchangeRateDialog from './ExchangeRateDialog.vue';

@Component({
  name: 'LogisticsExpensesStep2',
  components: { ExchangeRateDialog },
})
export default class LogisticsExpensesStep2 extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据

  $refs: any;
  loading: boolean = false;
  tableData: SpecialApplyLogisticsExpensesSummary[] = [];

  // 汇率相关
  exchangeRateDialogVisible: boolean = false;
  exchangeRateData: SpecialApplyCurrencyExchangeRate = new SpecialApplyCurrencyExchangeRate();
  created() {
    this.getExpensesSummary();
    this.getExchangeRateData();
  }

  // 获取费用明细
  getExpensesSummary() {
    this.loading = true;
    SpecialApplyBillLogisticsExpensesApi.getExpensesSummary(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.tableData = res.data.summaryList || [];
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }

  // 获取汇率数据
  getExchangeRateData() {
    SpecialApplyBillApi.listCurrencyExchangeRate(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.exchangeRateData = res.data;
        }
      })
      .catch((error) => {
        console.error('获取汇率数据失败:', error);
      });
  }

  // 显示汇率弹窗
  showExchangeRateDialog() {
    this.exchangeRateDialogVisible = true;
  }

  // 获取所有币种，用于显示
  get allCurrencies() {
    const currencies = new Set<string>();

    // 从物流费用汇总数据中获取币种
    // this.tableData.forEach((row) => {
    //   if (row.currencyIds) {
    //     row.currencyIds.forEach((currency) => {
    //       if (currency.currencyId) {
    //         currencies.add(currency.currencyId);
    //       }
    //     });
    //   }
    // });

    // 从汇率数据中获取币种
    this.exchangeRateData.targetCurrencyIds.forEach((currency) => {
      if (currency.currencyId) {
        currencies.add(currency.currencyId);
      }
    });

    return Array.from(currencies).join('、');
  }
  // 获取国内段运杂费（按币种分组）
  getDomesticFeesByCurrency(
    row: SpecialApplyLogisticsExpensesSummary
  ): { fees: any[]; tip: string } {
    const feesByCurrency = new Map<string, { amount: number; symbol: string }>();
    let missingItems: string[] = [];

    // 收集所有费用项的费用
    const feeItems = [
      { item: row.domesticTransportation, name: '国内运费' },
      { item: row.packing, name: '装箱费' },
      { item: row.packagingProtection, name: '包装防护费' },
    ];

    // 自营场景添加港杂费
    if (this.baseEntity.exportTypeId === 'self') {
      feeItems.push({ item: row.port, name: '港杂费' });
    }

    feeItems.forEach(({ item, name }) => {
      if (item?.ruleEmptyType) {
        // 明确标识的缺失类型："装运方案缺失"、"规则缺失"
        missingItems.push(name);
      } else if (item?.fees && item.fees.length > 0) {
        // 有费用数据，按币种累加
        item.fees.forEach((fee) => {
          if (fee.currencyId && fee.value && fee.value > 0) {
            const existing = feesByCurrency.get(fee.currencyId) || {
              amount: 0,
              symbol: fee.currencySymbol || '',
            };
            existing.amount += fee.value;
            feesByCurrency.set(fee.currencyId, existing);
          }
        });
      } else if (!item || !item.fees || item.fees.length === 0) {
        // 没有数据或费用为空，认为是规则缺失
        missingItems.push(name);
      }
    });

    // 生成提示信息
    const tip = missingItems.length > 0 ? missingItems.join('、') + '规则缺失' : '';

    // 转换为数组供模板使用
    const feesArray = Array.from(feesByCurrency.entries()).map(([currencyId, fee]) => ({
      currencyId,
      ...fee,
    }));

    return { fees: feesArray, tip };
  }

  // 获取国际段运杂费（按币种分组）
  getInternationalFeesByCurrency(
    row: SpecialApplyLogisticsExpensesSummary
  ): { fees: any[]; tip: string } {
    const feesByCurrency = new Map<string, { amount: number; symbol: string }>();
    let tip = '';

    const item = row.internationalTransportation;

    if (item?.ruleEmptyType) {
      // 明确标识的缺失类型："装运方案缺失"、"规则缺失"
      if (item.ruleEmptyType === '装运方案缺失') {
        tip = '未能找到装运方案，请联系物流部门补充';
      } else if (item.ruleEmptyType === '规则缺失') {
        tip = '未能找到国际运费规则，请联系物流部门补充';
      } else {
        tip = '未能找到国际运费规则，请联系物流部门补充';
      }
    } else if (item?.fees && item.fees.length > 0) {
      // 有费用数据，按币种设置
      item.fees.forEach((fee) => {
        if (fee.currencyId && fee.value && fee.value > 0) {
          feesByCurrency.set(fee.currencyId, {
            amount: fee.value,
            symbol: fee.currencySymbol || '',
          });
        }
      });
    } else if (!item || !item.fees || item.fees.length === 0) {
      // 没有数据或费用为空，认为是规则缺失
      tip = '未能找到国际运费规则，请联系物流部门补充';
    }

    // 转换为数组供模板使用
    const feesArray = Array.from(feesByCurrency.entries()).map(([currencyId, fee]) => ({
      currencyId,
      ...fee,
    }));

    return { fees: feesArray, tip };
  }

  // 保存数据
  async doSave(): Promise<void> {
    // 费用明细是只读的，不需要保存操作
    return Promise.resolve();
  }

  // 验证数据
  async doValidate(): Promise<boolean> {
    // 费用明细是只读的，不需要验证
    return true;
  }
}
