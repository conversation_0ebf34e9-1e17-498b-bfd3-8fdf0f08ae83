<template>
  <el-drawer
    ref="drawer"
    title="费用估算"
    direction="rtl"
    size="70%"
    :visible.sync="localValue"
    :before-close="handleClose"
  >
    <div class="drawer-body special-apply-cost-estimate">
      <el-form size="mini" ref="form" :model="logisticsEntity" :rules="rules" inline>
        <!-- 贸易术语（只有自营场景才显示） -->
        <el-form-item v-if="baseEntity.exportTypeId === ExportType.self" prop="incotermsId">
          <div class="incoterms">
            <!-- 自营-买断：只在物流费用-子步骤1可编辑 -->
            <template
              v-if="
                baseEntity.saleMode === GuidePriceType.buyOut &&
                  activeName === 'LogisticsExpenses' &&
                  activeSubTab === 1
              "
            >
              <span style="color: #f56c6c; margin-right: 4px;">*</span>
              <span class="incoterms-label">贸易术语</span>
              <el-select
                :value="logisticsEntity.incotermsId"
                placeholder="请选择"
                @change="handleIncotermsChange"
              >
                <el-option
                  v-for="item in incotermsOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </template>
            <!-- 自营-提佣：固定显示EXW -->
            <template v-else>
              <span class="incoterms-label"
                >贸易术语：{{ logisticsEntity.incotermsId || 'EXW' }}</span
              >
            </template>
          </div>
          <span
            v-if="
              baseEntity.saleMode === GuidePriceType.buyOut &&
                activeName === 'LogisticsExpenses' &&
                activeSubTab === 1
            "
            style="color: red; font-size: 12px; margin-left: 60px;"
          >
            如切换贸易术语会清空填写的信息
          </span>
        </el-form-item>
        <!-- Tab切换 -->
        <el-tabs v-model="activeName" type="card" :before-leave="beforeTabLeave">
          <!-- 物流费用 -->
          <el-tab-pane label="物流费用" name="LogisticsExpenses">
            <div class="logistics-expenses" v-if="activeName === 'LogisticsExpenses'">
              <SpecialApplyLogisticsExpenses
                ref="logisticsExpenses"
                :baseEntity="baseEntity"
                :entity="logisticsEntity"
                :activeSubTab="activeSubTab"
                @step-change="handleLogisticsStepChange"
              />
            </div>
          </el-tab-pane>
          <!-- 其他费用 -->
          <el-tab-pane label="其他费用" name="OtherExpenses">
            <div class="other-expenses" v-if="activeName === 'OtherExpenses'">
              <SpecialApplyOtherExpenses
                ref="otherExpenses"
                :baseEntity="baseEntity"
                :activeSubTab="activeSubTab"
                @step-change="handleOtherStepChange"
                @refresh="handleRefresh"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </div>
    <div class="drawer-footer">
      <!-- 保存并关闭、取消并关闭 按钮 -->
      <template v-if="showSaveButtons">
        <el-button size="mini" type="primary" @click="handleConfirm" :loading="importLoading">
          保存并关闭
        </el-button>
        <el-button size="mini" @click="handleClose">取消并关闭</el-button>
      </template>
      <!-- 确认并应用、关闭 按钮 -->
      <template v-if="showApplyButtons">
        <el-button size="mini" type="primary" @click="handleApply" :loading="importLoading">
          确认并应用
        </el-button>
        <el-button size="mini" @click="handleCloseOnly">关闭</el-button>
      </template>
    </div>
  </el-drawer>
</template>

<script src="./SpecialApplyCostEstimate.ts"></script>

<style lang="scss" scoped>
.special-apply-cost-estimate {
  .incoterms {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #242633;
    .incoterms-label {
      margin-right: 8px;
    }
  }

  .other-expenses {
    .step4-main {
      flex: 1;
      display: flex;
      border-radius: 4px;
      border: 1px solid #d7dfeb;
      margin-bottom: 12px;
      &-tab {
        flex-shrink: 0;
        flex-grow: 0;
        width: 160px;
        color: #79879e;
        background-color: #f7f9fc;

        &-item {
          display: flex;
          align-items: center;
          height: 56px;
          padding: 0px 12px;
          cursor: pointer;
          .item-icon {
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            border-radius: 10px;
            background-color: #e5e8eb;
            margin-right: 12px;
          }
          .item-text {
            font-size: 14px;
          }
        }

        &-active {
          background-color: #fff;
          .item-icon {
            font-weight: bold;
            color: #fff;
            background-color: $--color-primary;
          }
          .item-text {
            font-weight: bold;
            color: $--color-primary;
          }
        }
      }

      &-content {
        width: 0px;
        flex-grow: 1;
        flex-shrink: 1;
        padding: 12px;
      }
    }
  }
}

/deep/ .el-drawer {
  .el-drawer__header {
    height: 40px;
    line-height: 40px;
    color: #242633;
    font-size: 14px;
    padding: 0px 20px;
    margin-bottom: 16px;
  }
  .el-drawer__body {
    padding: 0px 20px;
    .drawer-body {
      height: calc(100% - 48px);
      overflow-x: hidden;
      overflow-y: auto;
    }
    .drawer-footer {
      height: 48px;
      padding: 8px 0px 12px 0px;
    }
  }
}
/deep/ .el-form-item {
  margin-bottom: 8px;
  .el-form-item__content {
    line-height: 24px;
  }
}
</style>
