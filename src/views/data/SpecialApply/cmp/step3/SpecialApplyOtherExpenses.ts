import { Component, Prop, Vue } from 'vue-property-decorator';

// MODELS
import SpecialApplyBill from 'model/remote/price/api/specialapply/bill/SpecialApplyBill';
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType';
// COMPONENTS
import OtherExpensesStep1 from './costEstimate/OtherExpensesStep1.vue';
import OtherExpensesStep2 from './costEstimate/OtherExpensesStep2.vue';
import OtherExpensesStep3 from './costEstimate/OtherExpensesStep3.vue';

@Component({
  name: 'SpecialApplyOtherExpenses',
  components: {
    OtherExpensesStep1,
    OtherExpensesStep2,
    OtherExpensesStep3,
  },
})
export default class SpecialApplyLogisticsExpenses extends Vue {
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill;

  @Prop({ type: Object, default: () => ({}) })
  entity: any;

  @Prop({ type: Number, default: 1 })
  activeSubTab: number;

  $refs: any;

  // 枚举引用
  ExportType = ExportType;

  // 子步骤列表
  tabList = [
    { step: 1, name: '填写费用' },
    { step: 2, name: '付款方式' },
    { step: 3, name: '费用汇总' },
  ];

  // 表单校验规则
  rules: any = {};

  // 子步骤切换
  async handleStepTabClick(step: number) {
    if (step === this.activeSubTab) {
      return;
    }

    // 如果是步骤1或步骤2，需要先保存当前步骤
    if (this.activeSubTab === 1 || this.activeSubTab === 2) {
      const currentRef = this.getCurrentStepRef();
      if (currentRef && currentRef.doSave) {
        try {
          await currentRef.doSave();
          // 保存成功后切换步骤
          this.$emit('step-change', step);
        } catch (error) {
          console.error('保存失败:', error);
          this.$message.error((error as any).message || '保存失败');
        }
      } else {
        // 没有找到子组件，直接切换
        this.$emit('step-change', step);
      }
    } else {
      // 步骤3（费用汇总）不需要保存，直接切换
      this.$emit('step-change', step);
    }
  }

  // 保存当前步骤（供外部调用）
  async doSaveCurrentStep() {
    const currentRef = this.getCurrentStepRef();
    if (currentRef && currentRef.doSave) {
      await currentRef.doSave();
    }
  }

  // 获取当前步骤组件引用
  getCurrentStepRef() {
    return this.$refs[`otherStep${this.activeSubTab}`];
  }

  // 保存整个模块
  async doSave() {
    return await this.doSaveCurrentStep();
  }

  // 处理子组件的refresh事件
  handleRefresh() {
    // 向上传递refresh事件给父组件
    this.$emit('refresh');
  }
}
