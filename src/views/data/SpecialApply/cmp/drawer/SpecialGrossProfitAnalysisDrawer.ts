import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import PrecisionUtil from '@/utils/Precision';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import SpecialApplyProfitAnalysissApi from '@/http/price/controller/profitanalysis/SpecialApplyProfitAnalysissApi';
// MODELS
import SpecialApplyBill from '@/model/remote/price/api/specialapply/bill/SpecialApplyBill';
import { ProfitAnalysisType } from 'model/remote/price/api/profitanalysis/ProfitAnalysisType';
import { SpecialApplyProfitAnalysisType } from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysisType';
import { SpecialApplyProfitAnalysissExchangeRateType } from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysissExchangeRateType';
import BSpecialApplyProfitAnalysis from 'model/remote/price/controller/profitanalysis/BSpecialApplyProfitAnalysis';
import SpecialApplyProfitAnalysisQueryRequest from 'model/remote/price/controller/profitanalysis/SpecialApplyProfitAnalysisQueryRequest';
import SpecialApplyProfitAnalysissExchangeRateLine from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplyProfitAnalysissExchangeRateLine';
import SpecialApplySupplyProfitAnalysis from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplySupplyProfitAnalysis';
import SpecialApplySelfCommissionExwProfitAnalysis from 'model/remote/price/api/profitanalysis/specialapply/SpecialApplySelfCommissionExwProfitAnalysis';
import { ExportType } from 'model/remote/price/model/po/subsidiarycommissionrate/ExportType';
import { GuidePriceType } from 'model/remote/price/model/po/GuidePriceType';

// COMPONENTES
import Panel from '@/components/panel/Panel.vue';

@Component({
  name: 'SpecialGrossProfitAnalysisDrawer',
  components: { Panel },
})
export default class SpecialGrossProfitAnalysisDrawer extends Vue {
  @Prop({ type: Boolean, default: false }) value: boolean;
  @Prop({ type: Array, default: () => [] })
  exchangeRateOptions; // 汇率类型
  @Prop({ type: Object, default: new SpecialApplyBill() })
  baseEntity: SpecialApplyBill; // 基础数据
  @Prop({
    type: Object,
    default: () => {
      return null;
    },
  })
  data: any; // 基础数据

  ProfitAnalysisType = ProfitAnalysisType;
  $refs: any;
  $echarts: any;
  loading: boolean = false;
  entity: any = null; // 毛利分析单台数据
  // 明细分析表格数据
  detailAnalyseList: any = [];
  // 第二个明细分析表格数据
  otherDetailAnalyseList: any = [];
  // 当前选择的汇率类型——默认为年度预算汇率（annualBudget）
  currentExchangeRate: Nullable<SpecialApplyProfitAnalysissExchangeRateType> =
    SpecialApplyProfitAnalysissExchangeRateType.annualBudget;
  // 整单表格数据
  tableData: any = [];
  ObjectUtil = ObjectUtil;

  get localValue() {
    return this.value;
  }

  set localValue(val) {
    this.$emit('input', val);
  }

  // 标题
  get grossProfitAnalysisDrawertitle() {
    return '查看单台毛利分析';
  }

  // 汇率显示内容
  get exchangeRate() {
    const result = this.exchangeRateOptions.find((item) => item.type === this.currentExchangeRate);
    if (result) {
      const list: string[] = result.exchangeRateList.reduce((cur, item) => {
        if (item.exchangeRate !== null) {
          // 保证展示四舍五入之后的四位小数
          let formatExchangeRate = parseFloat(item.exchangeRate.toFixed(4));
          cur.push(
            `${Vue.filter('text')(item.sourceCcy)}：${Vue.filter('text')(
              item.targetCcy
            )} = ${Vue.filter('text')(formatExchangeRate)}`
          );
        }
        return cur;
      }, []);
      return list.join('、');
    } else {
      return '--';
    }
  }

  // 供货
  get isSupplyType() {
    return (
      this.baseEntity.exportTypeId === ExportType.supply ||
      this.entity?.profitAnalysisType === SpecialApplyProfitAnalysisType.SUPPLY ||
      (this.entity?.profitAnalysis &&
        this.entity?.profitAnalysis[0]?.profitAnalysisType ===
          SpecialApplyProfitAnalysisType.SUPPLY)
    );
  }

  // 自营-提佣(EXW)
  get isCommissionType() {
    return (
      (this.baseEntity.saleMode === GuidePriceType.commission &&
        this.baseEntity.exportTypeId === ExportType.self) ||
      this.entity?.profitAnalysisType === SpecialApplyProfitAnalysisType.SELF_COMMISSION_EXW ||
      (this.entity?.profitAnalysis &&
        this.entity?.profitAnalysis[0]?.profitAnalysisType ===
          SpecialApplyProfitAnalysisType.SELF_COMMISSION_EXW)
    );
  }

  // 自营-买断
  get isSelloutType() {
    return (
      (this.baseEntity.saleMode === GuidePriceType.buyOut &&
        this.baseEntity.exportTypeId === ExportType.self) ||
      this.entity?.profitAnalysisType === SpecialApplyProfitAnalysisType.SELF_BUY ||
      (this.entity?.profitAnalysis &&
        this.entity?.profitAnalysis[0]?.profitAnalysisType ===
          SpecialApplyProfitAnalysisType.SELF_BUY)
    );
  }

  @Watch('value', { deep: true })
  localValueChange(val) {
    if (val) {
      this.changeExchangeRate();
    }
  }

  // 处理图表
  initCharts(list) {
    this.$nextTick(() => {
      let myChart = this.$echarts.init(this.$refs.chart);
      const allNumList = list.reduce((cur, item) => {
        return cur.concat([item.profitRate || 0, item.annualPricingProfitRate || 0]);
      }, []);
      // 产品线利率
      let productLineRateList: any = [];
      // 子公司利率
      let subsidiaryRateList: any = [];
      // 国际部利率
      let internationalDepartmentRateList: any = [];
      // 生产事业部利率
      let productionRateList: any = [];
      // 平均刻度
      const interval: number = 10;
      // 给图表用的Y轴最小值和最大值
      let maxNum: number = 100;
      let minNum: number = 0;
      if (list.length > 0) {
        // 产品线利率
        productLineRateList = [list[0].profitRate, list[0].annualPricingProfitRate];
        // 子公司利率
        subsidiaryRateList = [list[1].profitRate, list[1].annualPricingProfitRate];
        // 国际部利率
        internationalDepartmentRateList = [list[2].profitRate, list[2].annualPricingProfitRate];
        // 生产事业部利率
        productionRateList = [list[3].profitRate, list[3].annualPricingProfitRate];
        // 找到利率的最大最小值
        const maxVal = Math.max.apply(null, allNumList);
        const minVal = Math.min.apply(null, allNumList);
        if (maxVal < 0) {
          maxNum =
            Math.abs(maxVal) % interval
              ? maxVal + (Math.abs(maxVal) % interval) - interval
              : maxVal;
        } else {
          maxNum = maxVal % interval ? maxVal - (maxVal % interval) + interval : maxVal;
        }
        if (minVal < 0) {
          minNum =
            Math.abs(minVal) % interval
              ? minVal + (Math.abs(minVal) % interval) - interval
              : minVal;
        } else {
          minNum = Math.abs(minVal) % interval ? minVal - (minVal % interval) : minVal;
        }
      }
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            crossStyle: {
              color: '#999',
            },
          },
        },
        legend: {
          data: ['毛利率_子公司', '毛利率_国际', '毛利率_生产事业部', '毛利率_产品线'],
          bottom: 0,
        },
        xAxis: [
          {
            type: 'category',
            data: ['特价毛利率', '年度定价毛利率'],
            axisPointer: {
              type: 'shadow',
            },
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '毛利率',
            min: minNum,
            max: maxNum,
            interval: interval,
            axisLabel: {
              formatter: '{value}%',
            },
          },
        ],
        series: [
          {
            name: '毛利率_子公司',
            // barWidth: '1000', //---柱形宽度
            type: 'bar',
            tooltip: {
              valueFormatter: function(value) {
                return value + '%';
              },
            },
            data: subsidiaryRateList,
            itemStyle: {
              normal: {
                label: {
                  show: true, //开启显示
                  position: 'top',
                  textStyle: {
                    //数值样式
                    color: '#3B71FC',
                    fontSize: 12,
                  },
                  formatter: function(data) {
                    return '子公司 : ' + data.value + '%';
                  },
                },
                color: '#3B71FC',
              },
            },
          },
          {
            name: '毛利率_国际',
            type: 'bar',
            tooltip: {
              valueFormatter: function(value) {
                return value + '%';
              },
            },
            data: internationalDepartmentRateList,
            itemStyle: {
              normal: {
                label: {
                  show: true, //开启显示
                  position: 'top',
                  textStyle: {
                    //数值样式
                    color: '#58B929',
                    fontSize: 12,
                  },
                  formatter: function(data) {
                    return '国际 : ' + data.value + '%';
                  },
                },
                color: '#58B929',
              },
            },
          },
          {
            name: '毛利率_生产事业部',
            type: 'bar',
            tooltip: {
              valueFormatter: function(value) {
                return value + '%';
              },
            },
            data: productionRateList,
            itemStyle: {
              normal: {
                label: {
                  show: true, //开启显示
                  position: 'top',
                  textStyle: {
                    //数值样式
                    color: '#FFA001',
                    fontSize: 12,
                  },
                  formatter: function(data) {
                    return '生产事业部 : ' + data.value + '%';
                  },
                },
                color: '#FFA001',
              },
            },
          },
          {
            name: '毛利率_产品线',
            type: 'line',
            yAxisIndex: 0,
            tooltip: {
              valueFormatter: function(value) {
                return value + '%';
              },
            },
            data: productLineRateList,
            itemStyle: {
              normal: {
                label: {
                  show: true, //开启显示
                  position: 'top',
                  textStyle: {
                    //数值样式
                    color: '#DE3232',
                    fontSize: 12,
                  },
                  formatter: function(data) {
                    return '产品线 : ' + data.value + '%';
                  },
                },
                color: '#DE3232',
              },
            },
          },
        ],
      };
      myChart.setOption(option);
    });
  }

  // 处理页面数据
  handleData() {
    this.loading = true;
    // 单台毛利分析

    // 报价不含费用!this.baseEntity.otherExpenses，暂时只考虑不含费用的两种情况
    // if (!this.baseEntity.otherExpenses)
    this.tableData = [];
    const result = this.entity ? this.entity : new BSpecialApplyProfitAnalysis();
    // 供货
    if (this.isSupplyType) {
      const supply = result.supply as SpecialApplySupplyProfitAnalysis;
      const annualPricingSupply = result.annualPricingSupply as SpecialApplySupplyProfitAnalysis;
      this.detailAnalyseList = [
        {
          item: '工厂区域交货净价（含税）',
          salePriceCurrencySymbol: supply.netDeliveryPriceInFactoryAreaIncludingTax?.currencySymbol,
          salePrice: supply.netDeliveryPriceInFactoryAreaIncludingTax?.value,
          annualPriceCurrencySymbol:
            annualPricingSupply.netDeliveryPriceInFactoryAreaIncludingTax?.currencySymbol,
          annualPrice: annualPricingSupply.netDeliveryPriceInFactoryAreaIncludingTax?.value,
          isHighlight: true,
          isNotDisplay:
            supply.netDeliveryPriceInFactoryAreaIncludingTax?.value === null &&
            annualPricingSupply.netDeliveryPriceInFactoryAreaIncludingTax?.value === null,
        },
        {
          item: '工厂区域交货净价（不含税）',
          salePriceCurrencySymbol: supply.netDeliveryPriceInFactoryAreaExcludingTax?.currencySymbol,
          salePrice: supply.netDeliveryPriceInFactoryAreaExcludingTax?.value,
          annualPriceCurrencySymbol:
            annualPricingSupply.netDeliveryPriceInFactoryAreaExcludingTax?.currencySymbol,
          annualPrice: annualPricingSupply.netDeliveryPriceInFactoryAreaExcludingTax?.value,
          isHighlight: true,
          isNotDisplay:
            supply.netDeliveryPriceInFactoryAreaExcludingTax?.value === null &&
            annualPricingSupply.netDeliveryPriceInFactoryAreaExcludingTax?.value === null,
        },
        {
          item: '事业部关联结算价（不含税）',
          salePriceCurrencySymbol: supply.relatedSettlementPrice?.currencySymbol,
          salePrice: supply.relatedSettlementPrice?.value,
          annualPriceCurrencySymbol: annualPricingSupply.relatedSettlementPrice?.currencySymbol,
          annualPrice: annualPricingSupply.relatedSettlementPrice?.value,
          isHighlight: true,
          isNotDisplay:
            supply.relatedSettlementPrice?.value === null &&
            annualPricingSupply.relatedSettlementPrice?.value === null,
        },
        {
          item: '工厂出厂净价（不含税）',
          salePriceCurrencySymbol: supply.factoryNetPrice?.currencySymbol,
          salePrice: supply.factoryNetPrice?.value,
          annualPriceCurrencySymbol: annualPricingSupply.factoryNetPrice?.currencySymbol,
          annualPrice: annualPricingSupply.factoryNetPrice?.value,
          isHighlight: true,
          isNotDisplay:
            supply.factoryNetPrice?.value === null &&
            annualPricingSupply.factoryNetPrice?.value === null,
        },
        {
          item: '整机生产成本（不含税）',
          salePriceCurrencySymbol: supply.productionCostOfMachine?.currencySymbol,
          salePrice: supply.productionCostOfMachine?.value,
          annualPriceCurrencySymbol: annualPricingSupply.productionCostOfMachine?.currencySymbol,
          annualPrice: annualPricingSupply.productionCostOfMachine?.value,
          isHighlight: true,
          isNotDisplay:
            supply.productionCostOfMachine?.value === null &&
            annualPricingSupply.productionCostOfMachine?.value === null,
        },
      ];
    }
    // 提佣EXW
    if (this.isCommissionType) {
      const selfCommission = result.selfCommissionExw as SpecialApplySelfCommissionExwProfitAnalysis;
      const annualPricingSelfCommission = result.annualPricingSelfCommissionExw as SpecialApplySelfCommissionExwProfitAnalysis;
      this.detailAnalyseList = [
        {
          item: `经销商EXW价`,
          salePriceCurrencySymbol: selfCommission.dealerEXWFee?.currencySymbol,
          salePrice: selfCommission.dealerEXWFee?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.dealerEXWFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.dealerEXWFee?.value,
          isHighlight: true, // 是否高亮
        },
        {
          item: `远期贴现费`,
          salePriceCurrencySymbol: selfCommission.forwardDiscountFee?.currencySymbol,
          salePrice: selfCommission.forwardDiscountFee?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.forwardDiscountFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.forwardDiscountFee?.value,
          isHighlight: false,
        },
        {
          item: `信保费`,
          salePriceCurrencySymbol: selfCommission.creditInsurancePremium?.currencySymbol,
          salePrice: selfCommission.creditInsurancePremium?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.creditInsurancePremium?.currencySymbol,
          annualPrice: annualPricingSelfCommission.creditInsurancePremium?.value,
          isHighlight: false,
        },
        {
          item: `赠送配件金额`,
          salePriceCurrencySymbol: selfCommission.giftAccessoryFee?.currencySymbol,
          salePrice: selfCommission.giftAccessoryFee?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.giftAccessoryFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.giftAccessoryFee?.value,
          isHighlight: false,
        },
        {
          item: `延长保修费`,
          salePriceCurrencySymbol: selfCommission.extendedWarrantyFee?.currencySymbol,
          salePrice: selfCommission.extendedWarrantyFee?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.extendedWarrantyFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.extendedWarrantyFee?.value,
          isHighlight: false,
        },
        {
          item: `客户佣金`,
          salePriceCurrencySymbol: selfCommission.customerCommissionFee?.currencySymbol,
          salePrice: selfCommission.customerCommissionFee?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.customerCommissionFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.customerCommissionFee?.value,
          isHighlight: false,
        },
        {
          item: `额外费用`,
          salePriceCurrencySymbol: selfCommission.additionalFee?.currencySymbol,
          salePrice: selfCommission.additionalFee?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.additionalFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.additionalFee?.value,
          isHighlight: false,
        },
        {
          item: `经销商EXW净价`,
          salePriceCurrencySymbol: selfCommission.dealerNetPrice?.currencySymbol,
          salePrice: selfCommission.dealerNetPrice?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.dealerNetPrice?.currencySymbol,
          annualPrice: annualPricingSelfCommission.dealerNetPrice?.value,
          isHighlight: true,
        },
        {
          item: `子公司佣金率`,
          salePriceFeeRate: selfCommission.subsidiariesCommissionRate,
          annualPriceFeeRate: annualPricingSelfCommission.subsidiariesCommissionRate,
          isRate: true, // 是否是百分比
          isHighlight: true,
        },
        {
          item: `子公司佣金`,
          salePriceCurrencySymbol: selfCommission.subsidiariesCommission?.currencySymbol,
          salePrice: selfCommission.subsidiariesCommission?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.subsidiariesCommission?.currencySymbol,
          annualPrice: annualPricingSelfCommission.subsidiariesCommission?.value,
          isHighlight: false,
        },
        {
          item: `国际部EXW价`,
          salePriceCurrencySymbol: selfCommission.internationalEXWPrice?.currencySymbol,
          salePrice: selfCommission.internationalEXWPrice?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.internationalEXWPrice?.currencySymbol,
          annualPrice: annualPricingSelfCommission.internationalEXWPrice?.value,
          isHighlight: true,
        },
        {
          item: `事业部关联结算价`,
          salePriceCurrencySymbol: selfCommission.divisionSettlementFee?.currencySymbol,
          salePrice: selfCommission.divisionSettlementFee?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.divisionSettlementFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.divisionSettlementFee?.value,
          isHighlight: true,
        },
        {
          item: `国内运费（不含税）`,
          salePriceCurrencySymbol: selfCommission.domesticTransportationFee?.currencySymbol,
          salePrice: selfCommission.domesticTransportationFee?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.domesticTransportationFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.domesticTransportationFee?.value,
          isHighlight: false,
        },
        {
          item: `国内运输保险费`,
          salePriceCurrencySymbol:
            selfCommission.domesticTransportationInsuranceFee?.currencySymbol,
          salePrice: selfCommission.domesticTransportationInsuranceFee?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.domesticTransportationInsuranceFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.domesticTransportationInsuranceFee?.value,
          isHighlight: false,
        },
        {
          item: `港杂/口岸费`,
          salePriceCurrencySymbol: selfCommission.portFee?.currencySymbol,
          salePrice: selfCommission.portFee?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.portFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.portFee?.value,
          isHighlight: false,
        },
        {
          item: `装箱费（不含税）`,
          salePriceCurrencySymbol: selfCommission.packingFee?.currencySymbol,
          salePrice: selfCommission.packingFee?.value,
          annualPriceCurrencySymbol: annualPricingSelfCommission.packingFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.packingFee?.value,
          isHighlight: false,
        },
        {
          item: `包装防护费`,
          salePriceCurrencySymbol: selfCommission.packagingProtectionFee?.currencySymbol,
          salePrice: selfCommission.packagingProtectionFee?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.packagingProtectionFee?.currencySymbol,
          annualPrice: annualPricingSelfCommission.packagingProtectionFee?.value,
          isHighlight: false,
        },
        {
          item: `生产事业部出厂价`,
          salePriceCurrencySymbol: selfCommission.productionDivisionFactoryPrice?.currencySymbol,
          salePrice: selfCommission.productionDivisionFactoryPrice?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.productionDivisionFactoryPrice?.currencySymbol,
          annualPrice: annualPricingSelfCommission.productionDivisionFactoryPrice?.value,
          isHighlight: true,
        },
        {
          item: `整机生产成本`,
          salePriceCurrencySymbol: selfCommission.productionCostOfTheWholeMachine?.currencySymbol,
          salePrice: selfCommission.productionCostOfTheWholeMachine?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.productionCostOfTheWholeMachine?.currencySymbol,
          annualPrice: annualPricingSelfCommission.productionCostOfTheWholeMachine?.value,
          isHighlight: true,
        },
        {
          item: `EXW设备特价（包含子公司佣金）`,
          salePriceCurrencySymbol: selfCommission.machineSpecialOffer?.currencySymbol,
          salePrice: selfCommission.machineSpecialOffer?.value,
          annualPriceCurrencySymbol:
            annualPricingSelfCommission.machineSpecialOffer?.currencySymbol,
          annualPrice: annualPricingSelfCommission.machineSpecialOffer?.value,
          isHighlight: true,
        },
      ];
    }

    // 处理【不包含国内物料费用数据】
    if (this.isSupplyType || this.isCommissionType) {
      let data: any = null;
      let annualPricingData: any = null;
      if (this.isSupplyType) {
        data = result.supply as SpecialApplySupplyProfitAnalysis;
        annualPricingData = result.annualPricingSupply as SpecialApplySupplyProfitAnalysis;
      }
      if (this.isCommissionType) {
        data = result.selfCommissionExw as SpecialApplySelfCommissionExwProfitAnalysis;
        annualPricingData = result.annualPricingSelfCommissionExw as SpecialApplySelfCommissionExwProfitAnalysis;
      }
      this.otherDetailAnalyseList = [
        {
          dimension: '产品线',
          profitSymbol: data.productLineProfit?.currencySymbol,
          profitValue: data.productLineProfit?.value,
          profitRate:
            data.productLineRate === null
              ? data.productLineRate
              : PrecisionUtil.toFixed(PrecisionUtil.floatMul(data.productLineRate, 100), 2),
          annualPricingProfitSymbol: annualPricingData.productLineProfit?.currencySymbol,
          annualPricingProfitValue: annualPricingData.productLineProfit?.value,
          annualPricingProfitRate:
            annualPricingData.productLineRate === null
              ? annualPricingData.productLineRate
              : PrecisionUtil.toFixed(
                  PrecisionUtil.floatMul(annualPricingData.productLineRate, 100),
                  2
                ),
          isHighlight: true, // 是否高亮
        },
        {
          dimension: '子公司',
          profitSymbol: data.subsidiaryProfit?.currencySymbol,
          profitValue: data.subsidiaryProfit?.value,
          profitRate:
            data.subsidiaryRate === null
              ? data.subsidiaryRate
              : PrecisionUtil.toFixed(PrecisionUtil.floatMul(data.subsidiaryRate, 100), 2),
          annualPricingProfitSymbol: annualPricingData.subsidiaryProfit?.currencySymbol,
          annualPricingProfitValue: annualPricingData.subsidiaryProfit?.value,
          annualPricingProfitRate:
            annualPricingData.subsidiaryRate === null
              ? annualPricingData.subsidiaryRate
              : PrecisionUtil.toFixed(
                  PrecisionUtil.floatMul(annualPricingData.subsidiaryRate, 100),
                  2
                ),
          isHighlight: false, // 是否高亮
        },
        {
          dimension: '国际部',
          profitSymbol: data.internationalDepartmentProfit?.currencySymbol,
          profitValue: data.internationalDepartmentProfit?.value,
          profitRate:
            data.internationalDepartmentRate === null
              ? data.internationalDepartmentRate
              : PrecisionUtil.toFixed(
                  PrecisionUtil.floatMul(data.internationalDepartmentRate, 100),
                  2
                ),
          annualPricingProfitSymbol:
            annualPricingData.internationalDepartmentProfit?.currencySymbol,
          annualPricingProfitValue: annualPricingData.internationalDepartmentProfit?.value,
          annualPricingProfitRate:
            annualPricingData.internationalDepartmentRate === null
              ? annualPricingData.internationalDepartmentRate
              : PrecisionUtil.toFixed(
                  PrecisionUtil.floatMul(annualPricingData.internationalDepartmentRate, 100),
                  2
                ),
          isHighlight: false, // 是否高亮
        },
        {
          dimension: '生产事业部',
          profitSymbol: data.productionProfit?.currencySymbol,
          profitValue: data.productionProfit?.value,
          profitRate:
            data.productionRate === null
              ? data.productionRate
              : PrecisionUtil.toFixed(PrecisionUtil.floatMul(data.productionRate, 100), 2),
          annualPricingProfitSymbol: annualPricingData.productionProfit?.currencySymbol,
          annualPricingProfitValue: annualPricingData.productionProfit?.value,
          annualPricingProfitRate:
            annualPricingData.productionRate === null
              ? annualPricingData.productionRate
              : PrecisionUtil.toFixed(
                  PrecisionUtil.floatMul(annualPricingData.productionRate, 100),
                  2
                ),
          isHighlight: false, // 是否高亮
        },
      ];
    }
    this.initCharts(this.otherDetailAnalyseList || []);

    this.loading = false;
  }

  handleNum(val1, val2) {
    if (val1 === null || val1 === undefined || val2 === null || val2 === undefined) {
      return null;
    }
    return val1 * val2;
  }

  // 清空数据
  clearInfo() {
    this.detailAnalyseList = [];
    this.otherDetailAnalyseList = [];
    this.tableData = [];
    this.initCharts([]);
  }

  // 获取毛利分析信息
  changeExchangeRate() {
    const baseData = this.data;
    const params = new SpecialApplyProfitAnalysisQueryRequest();
    params.billNum = this.baseEntity.id;
    params.specialApplyMatOverviewId = baseData.id;

    const RateLine = new SpecialApplyProfitAnalysissExchangeRateLine();
    RateLine.type = this.currentExchangeRate;
    // 检验是否存在相应的汇率信息
    const list = this.exchangeRateOptions.filter((item) => item.type === this.currentExchangeRate);
    if (list.length > 0) {
      RateLine.exchangeRateList = list[0].exchangeRateList;
      const result = list[0].exchangeRateList.find((item) => item.exchangeRate === null);
      if (result) {
        this.$message.warning(
          `${result.sourceCcy}：${result.targetCcy}${this.handleRateType(
            this.currentExchangeRate
          )}汇率缺失，无法进行毛利分析，请先补全汇率信息`
        );
        this.clearInfo();
        return;
      }
      params.rateType = RateLine;
      // 调用特价申请的毛利分析计算接口
      SpecialApplyProfitAnalysissApi.specialApplyCal(params)
        .then((res) => {
          if (res.data) {
            this.entity = res.data;

            this.handleData();
          } else {
            this.detailAnalyseList = [];
            this.otherDetailAnalyseList = [];
          }
        })
        .catch((rej) => {
          this.$message.error(rej.message);
        });
    }
  }

  tableRowClassName({ row, rowIndex }) {
    if (row.isHighlight) {
      return 'hight-light-row';
    }
    if (row.isNotDisplay) {
      return 'hide-row';
    }
    return '';
  }

  // 根据费用项类型返回对应的小数位数
  getCostPricePrecision(item: string): number {
    // 成本价和关联结算价保留2位小数
    if (item.includes('整机生产成本') || item.includes('事业部关联结算价')) {
      return 2;
    }
    // 其他费用项取整数
    return 0;
  }

  handleRateType(type: Nullable<SpecialApplyProfitAnalysissExchangeRateType>) {
    if (type === SpecialApplyProfitAnalysissExchangeRateType.now) {
      return '最新汇率';
    } else if (type === SpecialApplyProfitAnalysissExchangeRateType.bill) {
      return '特价申请汇率';
    } else if (type === SpecialApplyProfitAnalysissExchangeRateType.annualBudget) {
      return '年度预算汇率';
    } else {
      return '--';
    }
  }
}
