/*
 * @Author: 张文轩
 * @Date: 2024-06-27 17:22:06
 * @LastEditTime: 2024-09-11 10:48:39
 * @LastEditors: 张文轩
 * @Description:
 * @FilePath: \ltc-web-ui\src\views\data\ContractQuotation\cmp\step3\TabStep5.ts
 * 记得注释
 */
import { Vue, Component, Prop, Watch } from 'vue-property-decorator';
import ObjectUtil from '@/utils/ObjectUtil';
// HTTPS
import LogisticsCostApi from '@/http/price/controller/quotationapply/logisticscost/LogisticsCostApi';
// MODELS
import BFeeDetail from '@/model/remote/price/controller/quotationapply/logisticscost/BFeeDetail';
import BQuotationApplyBill from '@/model/remote/price/controller/quotationapply/bill/BQuotationApplyBill';
// COMPONENTES
import FullscreenTable from './FullscreenTable.vue';

@Component({
  name: 'TabStep5',
  components: { FullscreenTable },
})
export default class TabStep5 extends Vue {
  @Prop({ type: Object, default: new BQuotationApplyBill() })
  baseEntity: BQuotationApplyBill; // 基础数据
  // @Prop({ type: Array, default: [] })
  // feeTypeList;
  $refs: any;
  entity: BFeeDetail = new BFeeDetail();
  loading: boolean = false;
  height: string = '400px';
  isFullscreen: boolean = false;
  tableData: any[] = [];
  created() {
    this.getLogisticsFees();
  }

  // 监听baseEntity.version变化，当设备报价等数据变化时重新获取物流费用
  @Watch('baseEntity.version')
  onBaseEntityVersionChange() {
    this.getLogisticsFees();
  }
  mounted() {
    this.resizeHandler();
    window.addEventListener('resize', this.resizeHandler);
  }
  // 获取费用明细
  getLogisticsFees() {
    this.loading = true;
    LogisticsCostApi.getLogisticsFees(this.baseEntity.id!)
      .then((res) => {
        if (res.data) {
          this.entity = res.data;
          this.tableData = this.getTableData(res.data.feeDetailLines);
          // this.getSummary(res.data.fees);
        }
      })
      .catch((rej) => {
        this.$message.error(rej.message);
      })
      .finally(() => {
        this.loading = false;
      });
  }
  // 构造系统推荐方案
  getTableData(result) {
    let arr = result.map((item) => {
      let obj: any = {};
      item.types.forEach((i) => {
        obj[i.id] = {
          name: i.name,
          amt: i.amt,
        };
      });
      const domesticFee = this.getDomesticFee(obj);
      return {
        ...item,
        ...obj,
        domesticFee,
      };
    });
    return arr;
  }
  // 汇总信息
  // getSummary(result) {
  //   this.$emit('summary', result || []);
  // }
  toggleFullscreen() {
    this.isFullscreen = !this.isFullscreen;
    // 整体全屏
    this.$fullscreen.toggle(this.$refs.table.$el, {
      teleport: true,
      pageOnly: false,
      fullscreenClass: 'table-fullscreen',
      callback: (isFullscreen) => {
        this.isFullscreen = isFullscreen;
        this.$emit('fullscreen', { isFullscreen });
      },
    });
  }
  // 保存数据
  doSave() {
    return new Promise((resolve, reject) => {
      resolve(true);
    });
  }
  // headerHeight = 52; // 头部标题栏
  // stepHeight = 56; // 步骤条
  // filterHeight = 138; // 筛选栏 - 两行 138; 单行非滚装散杂 70; 单行滚装散杂 98;
  // btnHeight = 52; // 底部按钮
  // padding = 12 * 3; // 边框
  resizeHandler() {
    this.height = window.innerHeight - 334 + 'px';
  }
  beforeDestroy() {
    window.removeEventListener('resize', this.resizeHandler);
  }
  // 获取国内段运杂费
  getDomesticFee(row) {
    let arr: number[] = [];
    if (this.baseEntity.exportTypeId === 'self') {
      arr = [201, 202, 203, 204]; // 国内段运杂费项对应id
    }
    if (this.baseEntity.exportTypeId === 'supply') {
      arr = [201, 203, 204]; // 国内段运杂费项对应id
    }
    let strArr: string[] = []; // 缺失的费用项
    let count: number = 0; // 费用项合计
    for (let i = 0; i < arr.length; i++) {
      if (ObjectUtil.isNullOrBlank(row[arr[i]].amt)) {
        strArr.push(row[arr[i]].name);
      } else {
        count += row[arr[i]].amt;
      }
    }
    if (strArr.length > 0) {
      return { tip: strArr.join('、') + '规则缺失', count: null };
    }
    return { tip: '', count: count };
  }
  // 获取国际段运杂费
  getInternational(row) {
    // 205
    return row[205].amt;
  }
}
