import { EnvType } from 'mgr/EnvType';
import LoginResultVo from 'model/local/micro/LoginResultVo';
import { localStorageMgr } from './utils/StorageUtil';
declare let window: any;
const devRemoteUrl = 'http://8391-test-baas-gateway.hd123.cn'; // 本地调试的后端地址
// const remoteContextPath = '/obs-service'; // 子项目后缀
const remoteContextPath = '/ltc-server'; // 子项目后缀
// uni的前端部署地址
const uniSessionKey = 'vuex';
const userInfoSessionKey = 'userInfo';
const deployType = process.env.VUE_APP_DEPLOY_TYPE || EnvType.uniDev; // 当前编译环境默认uniDev
const localDomain = window.location.protocol + '//' + window.location.host;
// 前端开发调试使用
const devLoginInfo = {
  accessToken: `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

`,
  refreshToken: `eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiJ9.eyJleHQuaXNzdWVTeXN0ZW0iOiJoZFBvcnRhbCIsInN1YiI6NDM3OTIzMzI4MjkxOTY2OTc2LCJpbnN0YW5jZSI6ImU4YTNlNmNkYWU5MzExZWU4YTE4MDA1MDU2ODE1YzgyIiwib3JpX3VzZXIiOiI0Mzc5MjMzMjgyOTE5NjY5NzYiLCJ0eXBlIjoiUkVGUkVTSCIsImV4dC5lbXBsb3llZUNvZGUiOiIwMDAxIiwiZXh0Lm9yZ05hbWUiOiLmgLvpg6giLCJleHQudXNlclNvdXJjZSI6IlBPUlRBTCIsImV4dC5lbXBsb3llZUlkIjoiMDAwMSIsImV4cCI6MTc1MzQwOTUyOCwiaWF0IjoxNzUzMzIzMTI4LCJqdGkiOjY0MTU4MDU3MDczNzQwNTk1Mywib3JpX25hbWVzcGFjZSI6ImNvbS5oZDEyMy51bmk6VXNlcjplOGEzZTZjZGFlOTMxMWVlOGExODAwNTA1NjgxNWM4MiIsInVuaV90ZW5hbnQiOiI4MzkxIiwiZXh0Lm9yZ0lkIjoiMSIsImV4dC5vcmdDb2RlIjoiMSIsImV4dGVybmFsSWQiOiI0Mzc5MjMzMjgyOTE5NjY5NzYiLCJleHQuZW1wbG95ZWVOYW1lIjoiYWRtaW4iLCJvcmlfdG9rZW4iOjY0MTU4MDU3MDczNzQwNTk1MiwibG9naW5fbmFtZSI6ImFkbWluIiwib3JpX2luc3RhbmNlIjoiZThhM2U2Y2RhZTkzMTFlZThhMTgwMDUwNTY4MTVjODIiLCJuYW1lc3BhY2UiOiJjb20uaGQxMjMudW5pOlVzZXI6ZThhM2U2Y2RhZTkzMTFlZThhMTgwMDUwNTY4MTVjODIiLCJuYW1lIjoiYWRtaW4iLCJvbmxpbmUiOmZhbHNlLCJleHQub3JnVHlwZSI6ImhxIn0.Kq4iKz_HPg6o2Ly7Wram5VtZaft_321JK8P8Crjq6vhEsucvEyZjHqSzEshWTr61Pv8y2jVLVZJ1vp27q74shaABfb5j24IlgnpubM3I3AB3h08xPLlg5BeHqmxZf9Qnk6tt6yAx39z_8c3riNGfDtw9PzW4CxXR_0tugD9qV_gTbFhukh_PG8gUes4UxYPl5WIuhwUToW4rnzOiipFqUK_m_SC-B9G8TNAETIzQlxUFyoeQKW84Km3ZoCABlaWCVgF15vfGWLiUBGjrRXI4XkaEJWSg-uxpDIGw4UTOVV59J5RrEuedBW6ibQ_0yrjOmaa60rq7bcfsOTL6Ku5rgQ
`,
};
const devUserInfo = {
  // employeeCode: '0001',
  // employeeId: '0001',
  // employeeName: 'admin',
  // messageRemind: true,
  // userId: '437923328291966976',
  // userName: 'admin',
  // voiceRemind: true,
  // workOrgCode: '1',
  // workOrgId: '1',
  // workOrgName: '总部',
  // workOrgType: 'hq',
  employeeCode: '0001',
  employeeId: '0001',
  employeeName: 'admin',
  messageRemind: true,
  userId: '437923328291966976',
  userName: 'admin',
  voiceRemind: true,
  workOrgCode: '50086376',
  workOrgId: '5753',
  workOrgName: '柳工机械俄罗斯有限公司',
  workOrgType: 'subCompany',
};
// 部署地址
// const deployUrl =
//   process.env.VUE_APP_BASEURL === 'https://osgateway.liugong.com'
//     ? 'https://prod-liugong-price.oss-cn-shenzhen.aliyuncs.com/ltc/'
//     : 'https://liugong-cpq-price.oss-cn-shenzhen.aliyuncs.com/ltc/';
// 后端服务地址
const baseUrl = process.env.VUE_APP_BASEURL || '';

// web应用环境变量
class AppContext {
  // 获取部署方式 cdn docker local
  public static deployType() {
    return deployType;
  }
  /**
   * 远程访问地址
   * @returns
   */
  public static getRemoteBaseUrl() {
    if (deployType === EnvType.uniDev) {
      return devRemoteUrl + remoteContextPath;
    } else {
      return baseUrl + remoteContextPath;
    }
  }
  /**
   * 静态导入模版地址
   * @returns
   */
  public static getTemplateUrl() {
    // 生产环境
    if (process.env.VUE_APP_BASEURL === 'https://osgateway.liugong.com') {
      return 'https://prod-liugong-price.oss-cn-shenzhen.aliyuncs.com/ltc/';
    }
    // uat环境
    if (process.env.VUE_APP_BASEURL === 'https://gatewaytest.liugong.vip') {
      return 'https://liugong-cpq-price.oss-cn-shenzhen.aliyuncs.com/ltc/';
    }
    return 'https://liugong-cpq-price.oss-cn-shenzhen.aliyuncs.com/ltc-test/';
  }
  // 后端访问地址
  public static remoteBaseUrl() {
    if (deployType === EnvType.uniDocker) {
      return process.env.VUE_APP_BASEURL + remoteContextPath;
    } else if (deployType === EnvType.uniDev) {
      return devRemoteUrl + remoteContextPath;
    } else {
      return localDomain + remoteContextPath;
    }
  }
  // 前端发布地址
  public static publicPath() {
    // 本应用前端部署的发布基路由 例如： /zl-portal-test/
    if (deployType === EnvType.uniCdn || deployType === EnvType.uniCdn) {
      let publicPath: string = process.env.VUE_APP_PUBLIC_PATH || '';
      if (!publicPath.endsWith('/')) {
        publicPath += '/';
      }
      return publicPath;
    } else if (deployType === EnvType.uniDev) {
      return '/';
    } else {
      return '';
    }
  }
  public static findLoginInfo() {
    let uniSession = localStorageMgr.getItem(uniSessionKey);

    if (uniSession) {
      return uniSession.loginInfo;
    } else {
      return {};
    }
  }
  public static findSeverDiffTime() {
    let uniSession = localStorageMgr.getItem(uniSessionKey);
    if (uniSession) {
      return uniSession.serverDiffTime || 0;
    } else {
      return 0;
    }
  }
  public static getToken(config) {
    // 获取loginInfo信息
    let loginInfo;
    // uni方案
    if (deployType === EnvType.uniDev) {
      loginInfo = devLoginInfo;
    } else {
      loginInfo = this.findLoginInfo();
    }
    // 存在有效corsToken时，不返回token
    let corsToken = null;
    if (config && config.params && config.params['cors-token']) {
      corsToken = config.params['cors-token'];
    }
    let hasCorsToken = true;
    if (corsToken === null) {
      hasCorsToken = false;
    }
    if (corsToken && loginInfo && loginInfo.usedCorsToken === corsToken) {
      hasCorsToken = false;
    }
    if (hasCorsToken) {
      return null;
    }
    return this.findUniToken(loginInfo);
  }
  public static findUniToken(loginInfo) {
    if (loginInfo == null || loginInfo.accessToken == null) {
      console.error('has no loginInfo.accessToken');
      return null;
    }
    const tokenArr: string[] = loginInfo.accessToken.split('.');
    if (tokenArr.length === 3) {
      // base64对于"_"、"-"要处理成"/"、"+"
      let tokenStr = tokenArr[1].replace(/-/g, '+').replace(/_/g, '/');
      const tokenInfo = JSON.parse(window.atob(tokenStr));
      const currentTime = Math.floor(new Date().getTime()); // 当前时间戳,向下取整
      // 判断token失效时间，若失效则将token替换成refreshToken
      let serverDiffTime = this.findSeverDiffTime();
      if (tokenInfo.exp * 1000 < currentTime + serverDiffTime + 300000) {
        // token失效
        return loginInfo.refreshToken;
      } else {
        // 未过失效时间
        return loginInfo.accessToken;
      }
    } else {
      return loginInfo.accessToken;
    }
  }
  /** 更新uni的token/serverDiffTime信息 */
  public static updateUniToken(response) {
    let loginInfo: LoginResultVo = new LoginResultVo();
    // 更新loginInfo
    if (response.headers['access-token']) {
      loginInfo.accessToken = response.headers['access-token'];
    }
    if (response.headers['refresh-token']) {
      loginInfo.refreshToken = response.headers['refresh-token'];
    }
    if (response.headers['used-cors-token']) {
      loginInfo.usedCorsToken = response.headers['used-cors-token'];
    }
    // 更新后台
    if (loginInfo.accessToken) {
      this.updateUniLoginInfo(loginInfo);
    }
    // 计算服务器与用户PC的时间差
    if (response.headers['server-time']) {
      const currentTime = Math.floor(new Date().getTime()); // 当前时间戳,向下取整
      const diffTime = response.headers['server-time'] - currentTime; // 时间差
      this.updateServerDiffTime(diffTime);
    }
  }
  public static updateUniLoginInfo(loginInfo) {
    let uni = this.getUniStorage();
    uni.loginInfo = loginInfo;
    localStorageMgr.setItem(uniSessionKey, uni);
  }
  public static updateServerDiffTime(diffTime) {
    let uni = this.getUniStorage();
    uni.diffTime = diffTime;
    localStorageMgr.setItem(uniSessionKey, uni);
  }
  public static getUniStorage() {
    let uniStorage = localStorageMgr.getItem(uniSessionKey);
    if (uniStorage) {
      return uniStorage;
    } else {
      return {};
    }
  }
  /**
   * 异常时是否跳过权限检查，供开发环境使用
   * @returns
   */
  public static skipPermission() {
    return deployType === EnvType.uniDev;
  }
  /** 从token中获取userInfo */
  public static getUserInfoByToken(token: string) {
    let userInfo: any = {};
    if (token) {
      const tokenArr: string[] = token.split('.');
      if (tokenArr.length === 3) {
        // base64对于"_"、"-"要处理成"/"、"+"
        let tokenStr = tokenArr[1].replace(/-/g, '+').replace(/_/g, '/');
        userInfo = JSON.parse(decodeURIComponent(escape(window.atob(tokenStr))));
      }
    }
    return userInfo;
  }
  /** 从缓存中获取中获取userInfo */
  public static getUserInfoBySessionStorage() {
    if (deployType === EnvType.uniDev) {
      return devUserInfo;
    } else {
      return localStorageMgr.getItem(userInfoSessionKey);
    }
  }
}

export { devLoginInfo, uniSessionKey, AppContext, userInfoSessionKey };
